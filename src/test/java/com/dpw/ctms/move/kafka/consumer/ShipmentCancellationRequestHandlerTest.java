package com.dpw.ctms.move.kafka.consumer;


import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorHeaderDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorMessageRequestDTO;
import com.dpw.ctms.move.dto.consumer.ShipmentCancellationMessageRequestDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.ShipmentTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TaskParam;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.integration.adapter.TaskServiceAdapter;
import com.dpw.ctms.move.kafka.producer.impl.KafkaProducerImpl;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.IShipmentTaskService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.StateMachineConfigReader;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.ctms.move.utils.Faker;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.mockito.Mockito.when;

@SpringBootTest
@Transactional
@AutoConfigureMockMvc
class ShipmentCancellationRequestHandlerTest extends BaseTest {
    @Autowired
    private ITaskService taskService;
    @Autowired
    private IShipmentService shipmentService;
    @Autowired
    private ITripService tripService;
    @Autowired
    private IShipmentTaskService shipmentTaskService;
    @Autowired
    private ShipmentCancellationRequestHandler shipmentCancellationRequestHandler;
    @MockBean
    private StateMachineServiceRegistry stateMachineServiceRegistry;
    @MockBean
    private StateMachineConfigReader stateMachineConfigReader;
    @MockBean
    private TaskServiceAdapter taskServiceAdapter;
    @MockBean
    private KafkaProducerImpl kafkaProducer;

    @BeforeEach
    void mockStateMachine() {
        IStateMachineService<?> mockStateMachine = Mockito.mock(IStateMachineService.class);
        Mockito.doNothing().when(mockStateMachine).handleEvent(Mockito.any(), Mockito.any(), Mockito.any());
        when(stateMachineServiceRegistry.getService(Mockito.any(StateMachineEntityType.class)))
                .thenAnswer(invocation -> mockStateMachine);
    }

    @Test
    void testShipmentCancellationRequest_withValidShipmentCode() {

        Trip trip = new Trip();
        trip.setActualStartAt(null);
        trip.setStatus(TripStatus.CREATED);

        // Create and initialize 2 stops for the trip
        Stop stop1 = new Stop();
        stop1.setCode("STOP1");
        stop1.setSequence(1);
        stop1.setTrip(trip);

        Stop stop2 = new Stop();
        stop2.setCode("STOP2");
        stop2.setSequence(2);
        stop2.setTrip(trip);

        Set<Stop> stops = new HashSet<>();
        stops.add(stop1);
        stops.add(stop2);
        trip.setStops(stops);

        trip = tripService.saveTrip(trip);

        Shipment shipment = new Shipment();
        shipment.setCode("SHIP1");
        shipment.setStatus(ShipmentStatus.ASSIGNED);
        shipment.setTrip(trip);
        shipment.setActualPickupAt(null);
        shipmentService.saveShipment(shipment);

        TaskParam taskParam = new TaskParam();
        taskParam.setParamName(TaskParamType.SHIPMENT);
        ParamValueShipmentDTO shipmentDTO = new ParamValueShipmentDTO();
        shipmentDTO.setCode("SHIP1");
        taskParam.setParamValue(ObjectMapperUtil.getObjectMapper().valueToTree(shipmentDTO));

        Task task = new Task();
        task.setCode("TASK123");
        task.setStatus(TaskStatus.CREATED);
        task.setTaskParams(new ArrayList<>(List.of(taskParam)));
        taskService.saveTask(task);

        ShipmentTask shipmentTask = new ShipmentTask();
        shipmentTask.setTask(task);
        shipmentTask.setShipment(shipment);
        shipmentTaskService.saveShipmentTask(shipmentTask);

        ShipmentCancellationMessageRequestDTO shipmentCancellationMessageRequestDTO =
                ShipmentCancellationMessageRequestDTO.builder()
                        .shipmentCodes(List.of("SHIP1") )
                        .updatedAt(String.valueOf(System.currentTimeMillis()))
                        .comments("Shipment cancellation request")
                        .build();

        IntegratorHeaderDTO headerDTO = IntegratorHeaderDTO.builder()
                .action("SHIPMENT_CANCELLATION_REQUEST")
                .dateTime(System.currentTimeMillis())
                .source("CTMS-MOVE")
                .topic("shipment-cancel-topic")
                .correlationId(UUID.randomUUID().toString())
                .build();

        IntegratorMessageRequestDTO<ShipmentCancellationMessageRequestDTO> integratorMessageRequestDTO =
                IntegratorMessageRequestDTO.<ShipmentCancellationMessageRequestDTO>builder()
                .transactionContext(headerDTO)
                .message(
                        IntegratorMessageRequestDTO.Message.<ShipmentCancellationMessageRequestDTO>builder()
                                .item(shipmentCancellationMessageRequestDTO)
                                .build()
                )
                .build();

        Mockito.when(taskServiceAdapter.deRegisterTaskInstance(Mockito.any()))
                .thenReturn(Collections.singletonList(Faker.createDummyTaskInstanceDeRegistrationResponse()));
        shipmentCancellationRequestHandler.handle(integratorMessageRequestDTO);
        Task updatedTask = taskService.findTaskById(task.getId());
        Assertions.assertEquals(TaskStatus.DISCARDED, updatedTask.getStatus());
    }
}