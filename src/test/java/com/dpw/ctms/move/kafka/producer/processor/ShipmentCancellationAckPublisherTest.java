package com.dpw.ctms.move.kafka.producer.processor;


import com.dpw.ctms.move.enums.MessageActionType;
import com.dpw.ctms.move.kafka.producer.KafkaProducer;
import com.dpw.ctms.move.kafka.producer.processor.impl.ShipmentCancellationAckPublisher;
import com.dpw.ctms.move.request.common.IntegratorMessageHeader;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.request.common.MessageRequest;
import com.dpw.ctms.move.request.message.ShipmentCancellationAckMessage;
import com.dpw.ctms.move.service.IEventProcessorService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ShipmentCancellationAckPublisherTest {

    private IEventProcessorService<ShipmentCancellationAckMessage> eventProcessorService;
    private KafkaProducer kafkaProducer;
    private ShipmentCancellationAckPublisher publisher;

    @BeforeEach
    void setUp() {
        eventProcessorService = mock(IEventProcessorService.class);
        kafkaProducer = mock(KafkaProducer.class);
        publisher = new ShipmentCancellationAckPublisher(eventProcessorService, kafkaProducer);
    }

    @Test
    void shouldRegisterItselfOnPostConstruct() {
        publisher.init();

        verify(eventProcessorService, times(1)).addRequestProcessor(
                eq(MessageActionType.SHIPMENT_CANCELLATION_CONFIRMATION_EVENT.name()), eq(publisher)
        );
    }

    @Test
    void shouldPublishEventSuccessfully() {
        ShipmentCancellationAckMessage message = ShipmentCancellationAckMessage.builder()
                .shipmentCodes(java.util.List.of("SHIP123"))
                .isCancelled(true)
                .build();

        IntegratorMessageRequest<ShipmentCancellationAckMessage> request =
                IntegratorMessageRequest.<ShipmentCancellationAckMessage>builder()
                        .transactionContext(IntegratorMessageHeader.builder()
                                .topic("shipment-cancel-topic")
                                .build())
                        .message(MessageRequest.<ShipmentCancellationAckMessage>builder()
                                .item(message)
                                .build())
                        .build();

        when(kafkaProducer.publishEvent(request)).thenReturn(true);

        boolean result = publisher.process(request);

        assertTrue(result);
        verify(kafkaProducer, times(1)).publishEvent(request);
    }

    @Test
    void shouldReturnFalseWhenEventPublishingFails() {
        ShipmentCancellationAckMessage message = ShipmentCancellationAckMessage.builder()
                .shipmentCodes(java.util.List.of("SHIP456"))
                .isCancelled(false)
                .build();


        IntegratorMessageRequest<ShipmentCancellationAckMessage> request =
                IntegratorMessageRequest.<ShipmentCancellationAckMessage>builder()
                        .transactionContext(IntegratorMessageHeader.builder()
                                .topic("shipment-cancel-topic")
                                .build())
                        .message(MessageRequest.<ShipmentCancellationAckMessage>builder()
                                .item(message)
                                .build())
                        .build();

        when(kafkaProducer.publishEvent(request)).thenReturn(false);

        boolean result = publisher.process(request);

        assertFalse(result);
        verify(kafkaProducer, times(1)).publishEvent(request);
    }
}