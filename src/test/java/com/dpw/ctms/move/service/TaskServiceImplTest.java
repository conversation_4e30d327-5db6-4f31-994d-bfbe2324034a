package com.dpw.ctms.move.service;

import com.dpw.ctms.move.constants.TaskServiceConstants;
import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.dto.taskmanager.TaskRegistrationDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.integration.adapter.TaskServiceAdapter;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;
import com.dpw.ctms.move.mapper.TaskInstanceRegistrationRequestMapper;
import com.dpw.ctms.move.repository.TaskRepository;
import com.dpw.ctms.move.service.impl.TaskParamService;
import com.dpw.ctms.move.service.impl.TaskServiceImpl;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskServiceImplTest {

    @Mock
    private TaskInstanceRegistrationRequestMapper taskInstanceRegistrationRequestMapper;

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private TaskServiceAdapter taskServiceAdapter;

    @Mock
    private TaskParamService taskParamService;

    @Mock
    private IVehicleOperatorService vehicleOperatorService;

    @InjectMocks
    private TaskServiceImpl taskService;

    private Task testTask;
    private TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO testTaskDTO;
    private ParamValueVehicleOperatorDTO testVehicleOperator;
    private GetOperatorDetailsListResponse testOperatorDetails;
    private TransportOrder testTransportOrder;

    @BeforeEach
    void setUp() {
        testTask = new Task();
        testTask.setId(1L);
        testTask.setCode("TASK001");

        testTaskDTO = new TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO();
        testTaskDTO.setExtTaskTransactionCode("TASK001");

        testVehicleOperator = new ParamValueVehicleOperatorDTO();
        testVehicleOperator.setExternalResourceId("OP001");

        // Mock CRP details
        GetOperatorDetailsListResponse.CrpDetailsResponse crpDetails = new GetOperatorDetailsListResponse.CrpDetailsResponse();
        crpDetails.setCrpUserUUID("CRP001");

        testOperatorDetails = new GetOperatorDetailsListResponse();
        testOperatorDetails.setCrpDetails(crpDetails);

        testTransportOrder = new TransportOrder();
        testTransportOrder.setId(1L);
        testTransportOrder.setCode("TO001");
    }

    @Test
    void findTaskById_WhenTaskExists_ShouldReturnTask() {
        // Arrange
        Long taskId = 1L;
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));

        // Act
        Task result = taskService.findTaskById(taskId);

        // Assert
        assertNotNull(result);
        assertEquals(testTask.getId(), result.getId());
        verify(taskRepository).findById(taskId);
    }

    @Test
    void findTaskById_WhenTaskNotExists_ShouldThrowTMSException() {
        // Arrange
        Long taskId = 999L;
        when(taskRepository.findById(taskId)).thenReturn(Optional.empty());

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () -> taskService.findTaskById(taskId));
        assertEquals(DATA_NOT_FOUND.name(), exception.getErrorCode());
//        assertTrue(exception.getMessage().contains(String.format(INVALID_TASK_ID, taskId)));
        verify(taskRepository).findById(taskId);
    }

    @Test
    void findTaskByCode_WhenTaskExists_ShouldReturnTask() {
        // Arrange
        String taskCode = "TASK001";
        when(taskRepository.findByCode(taskCode)).thenReturn(Optional.of(testTask));

        // Act
        Task result = taskService.findTaskByCode(taskCode);

        // Assert
        assertNotNull(result);
        assertEquals(testTask.getCode(), result.getCode());
        verify(taskRepository).findByCode(taskCode);
    }

    @Test
    void findTaskByCode_WhenTaskNotExists_ShouldThrowTMSException() {
        // Arrange
        String taskCode = "INVALID_CODE";
        when(taskRepository.findByCode(taskCode)).thenReturn(Optional.empty());

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () -> taskService.findTaskByCode(taskCode));
        assertEquals(DATA_NOT_FOUND.name(), exception.getErrorCode());
//        assertTrue(exception.getMessage().contains(String.format(INVALID_TASK_CODE, taskCode)));
        verify(taskRepository).findByCode(taskCode);
    }

    @Test
    void saveTask_ShouldReturnSavedTask() {
        // Arrange
        when(taskRepository.save(testTask)).thenReturn(testTask);

        // Act
        Task result = taskService.saveTask(testTask);

        // Assert
        assertNotNull(result);
        assertEquals(testTask, result);
        verify(taskRepository).save(testTask);
    }

    @Test
    void instantiateTasks_WhenTaskListIsNull_ShouldReturnEarly() {
        // Act
        taskService.instantiateTasks(null, testTransportOrder);

        // Assert
        verifyNoInteractions(taskParamService);
        verifyNoInteractions(vehicleOperatorService);
        verifyNoInteractions(taskServiceAdapter);
        verifyNoInteractions(taskRepository);
    }

    @Test
    void instantiateTasks_WhenTaskListIsEmpty_ShouldReturnEarly() {
        // Act
        taskService.instantiateTasks(Collections.emptyList(), testTransportOrder);

        // Assert
        verifyNoInteractions(taskParamService);
        verifyNoInteractions(vehicleOperatorService);
        verifyNoInteractions(taskServiceAdapter);
        verifyNoInteractions(taskRepository);
    }

    @Test
    void instantiateTasks_WithValidTasks_ShouldProcessSuccessfully() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), eq(TaskServiceConstants.DEFAULT_TENANT_CODE),
                eq(TaskServiceConstants.DEFAULT_TENANT_SERVICE_CODE), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        verify(taskParamService).getVehicleOperators(testTask);
        verify(vehicleOperatorService).fetchOperatorDetails(any());
        verify(taskServiceAdapter).registerTaskInstance(any(), anyString(), anyString(), anyString());
        assertEquals("REG001", testTask.getExternalTaskRegistrationCode());
    }

    @Test
    void instantiateTasks_WithTasksHavingNoVehicleOperators_ShouldProcessWithControllerRoleOnly() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> emptyVehicleOperators = Collections.emptyList();

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(emptyVehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(Collections.emptyMap());
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        verify(taskParamService).getVehicleOperators(testTask);
        verify(vehicleOperatorService).fetchOperatorDetails(any());
        verify(taskServiceAdapter).registerTaskInstance(any(), anyString(), anyString(), anyString());
    }

    @Test
    void instantiateTasks_WithVehicleOperatorsButNullExternalResourceId_ShouldSkipOperator() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        ParamValueVehicleOperatorDTO operatorWithNullId = new ParamValueVehicleOperatorDTO();
        operatorWithNullId.setExternalResourceId(null);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(operatorWithNullId);

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(Collections.emptyMap());
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        verify(vehicleOperatorService).fetchOperatorDetails(Collections.emptyMap());
    }

    @Test
    void instantiateTasks_WithOperatorDetailsButNoCrpDetails_ShouldLogWarning() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);

        GetOperatorDetailsListResponse operatorWithoutCrp = new GetOperatorDetailsListResponse();
        operatorWithoutCrp.setCrpDetails(null);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", operatorWithoutCrp);

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        // No database save should occur in instantiateTasks
    }

    @Test
    void instantiateTasks_WithMissingOperatorDetails_ShouldLogWarning() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);
        Map<String, GetOperatorDetailsListResponse> emptyOperatorDetailsMap = Collections.emptyMap();

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(emptyOperatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        // No database save should occur in instantiateTasks
    }

    @Test
    void instantiateTasks_WithResponseForNonExistentTask_ShouldLogWarning() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> emptyVehicleOperators = Collections.emptyList();

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("NON_EXISTENT_TASK");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(emptyVehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(Collections.emptyMap());
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        assertNull(testTask.getExternalTaskRegistrationCode());
    }

    @Test
    void instantiateTasks_WithMultipleTasks_ShouldProcessAllTasks() {
        // Arrange
        Task task2 = new Task();
        task2.setId(2L);
        task2.setCode("TASK002");

        List<Task> taskList = Arrays.asList(testTask, task2);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO task2DTO =
                new TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO();
        task2DTO.setExtTaskTransactionCode("TASK002");

        TaskInstanceRegistrationResponse response1 = new TaskInstanceRegistrationResponse();
        response1.setExtTaskTransactionCode("TASK001");
        response1.setTaskRegistrationCode("REG001");

        TaskInstanceRegistrationResponse response2 = new TaskInstanceRegistrationResponse();
        response2.setExtTaskTransactionCode("TASK002");
        response2.setTaskRegistrationCode("REG002");

        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response1, response2);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(taskParamService.getVehicleOperators(task2)).thenReturn(Collections.emptyList());
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(task2))
                .thenReturn(task2DTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        verify(taskParamService).getVehicleOperators(testTask);
        verify(taskParamService).getVehicleOperators(task2);
        assertEquals("REG001", testTask.getExternalTaskRegistrationCode());
        assertEquals("REG002", task2.getExternalTaskRegistrationCode());
    }

    // Consolidated test covering CRP ID population with various scenarios
    @Test
    void populateCrpIdInVehicleOperatorResources_WithVariousScenarios_ShouldHandleAllCases() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        // Create multiple VehicleOperatorResources with different scenarios
        VehicleOperatorResource resource1 = new VehicleOperatorResource();
        resource1.setExternalResourceId("1"); // Will match operator ID 1
        resource1.setCrpId(null);

        VehicleOperatorResource resource2 = new VehicleOperatorResource();
        resource2.setExternalResourceId(null); // Null external resource ID
        resource2.setCrpId(null);

        VehicleOperatorResource resource3 = new VehicleOperatorResource();
        resource3.setExternalResourceId("999"); // Non-matching ID
        resource3.setCrpId(null);

        // Create trips with different scenarios
        Trip trip1 = new Trip();
        trip1.setVehicleOperatorResources(Set.of(resource1, resource2, resource3));

        Trip trip2 = new Trip();
        trip2.setVehicleOperatorResources(null); // Null vehicle operator resources

        testTransportOrder.setTrips(Set.of(trip1, trip2));

        // Create operator details
        GetOperatorDetailsListResponse operatorDetails1 = new GetOperatorDetailsListResponse();
        operatorDetails1.setId(1L);
        GetOperatorDetailsListResponse.CrpDetailsResponse crpDetails1 = new GetOperatorDetailsListResponse.CrpDetailsResponse();
        crpDetails1.setCrpUserUUID("CRP001");
        operatorDetails1.setCrpDetails(crpDetails1);

        GetOperatorDetailsListResponse operatorDetailsNoCrp = new GetOperatorDetailsListResponse();
        operatorDetailsNoCrp.setId(2L);
        operatorDetailsNoCrp.setCrpDetails(null); // No CRP details

        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", operatorDetails1, "OP002", operatorDetailsNoCrp);

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        assertEquals("CRP001", resource1.getCrpId()); // Successful match
        assertNull(resource2.getCrpId()); // Null external resource ID
        assertNull(resource3.getCrpId()); // Non-matching ID
        verify(taskParamService).getVehicleOperators(testTask);
        verify(vehicleOperatorService).fetchOperatorDetails(any());
    }

    @Test
    void populateCrpIdInVehicleOperatorResources_WithNullTrips_ShouldHandleGracefully() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        testTransportOrder.setTrips(null); // Test null trips scenario

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act & Assert - Should not throw exception
        assertDoesNotThrow(() -> taskService.instantiateTasks(taskList, testTransportOrder));
        verify(taskParamService).getVehicleOperators(testTask);
        verify(vehicleOperatorService).fetchOperatorDetails(any());
    }

    // Consolidated test covering extractCrpIdFromOperatorDetails with all edge cases
    @Test
    void extractCrpIdFromOperatorDetails_WithVariousScenarios_ShouldHandleAllCases() {
        // Test 1: Valid data - should return CRP ID
        testOperatorDetails.setId(1L);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);
        assertEquals("CRP001", invokeExtractCrpIdFromOperatorDetails("1", operatorDetailsMap));

        // Test 2: Null external resource ID - should return null
        assertNull(invokeExtractCrpIdFromOperatorDetails(null, operatorDetailsMap));

        // Test 3: Non-matching external resource ID - should return null
        assertNull(invokeExtractCrpIdFromOperatorDetails("999", operatorDetailsMap));

        // Test 4: Null CRP details - should return null
        GetOperatorDetailsListResponse operatorWithoutCrp = new GetOperatorDetailsListResponse();
        operatorWithoutCrp.setId(1L);
        operatorWithoutCrp.setCrpDetails(null);
        Map<String, GetOperatorDetailsListResponse> mapWithoutCrp =
                Map.of("OP001", operatorWithoutCrp);
        assertNull(invokeExtractCrpIdFromOperatorDetails("1", mapWithoutCrp));

        // Test 5: Null operator ID - should return null
        GetOperatorDetailsListResponse operatorWithNullId = new GetOperatorDetailsListResponse();
        operatorWithNullId.setId(null);
        operatorWithNullId.setCrpDetails(testOperatorDetails.getCrpDetails());
        Map<String, GetOperatorDetailsListResponse> mapWithNullId =
                Map.of("OP001", operatorWithNullId);
        assertNull(invokeExtractCrpIdFromOperatorDetails("1", mapWithNullId));

        // Test 6: Empty operator details map - should return null
        assertNull(invokeExtractCrpIdFromOperatorDetails("1", Collections.emptyMap()));
    }

    @Test
    void instantiateTasks_WithCrpIdPopulation_ShouldCompleteFullFlow() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        // Create VehicleOperatorResource
        VehicleOperatorResource vehicleOperatorResource = new VehicleOperatorResource();
        vehicleOperatorResource.setExternalResourceId("1");
        vehicleOperatorResource.setCrpId(null);

        Trip trip = new Trip();
        trip.setVehicleOperatorResources(Set.of(vehicleOperatorResource));
        testTransportOrder.setTrips(Set.of(trip));

        testOperatorDetails.setId(1L);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);
        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        // Verify CRP ID was populated
        assertEquals("CRP001", vehicleOperatorResource.getCrpId());

        // Verify task registration code was set
        assertEquals("REG001", testTask.getExternalTaskRegistrationCode());

        // Verify all services were called
        verify(taskParamService).getVehicleOperators(testTask);
        verify(vehicleOperatorService).fetchOperatorDetails(any());
        verify(taskServiceAdapter).registerTaskInstance(any(), anyString(), anyString(), anyString());

        // Verify no direct task repository save was called
        verify(taskRepository, never()).saveAll(any());
        verify(taskRepository, never()).save(any());
    }

    // Helper method to invoke private extractCrpIdFromOperatorDetails method using reflection
    private String invokeExtractCrpIdFromOperatorDetails(String externalResourceId,
                                                        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap) {
        try {
            Method method = TaskServiceImpl.class.getDeclaredMethod("extractCrpIdFromOperatorDetails",
                    String.class, Map.class);
            method.setAccessible(true);
            return (String) method.invoke(taskService, externalResourceId, operatorDetailsMap);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke extractCrpIdFromOperatorDetails", e);
        }
    }
}