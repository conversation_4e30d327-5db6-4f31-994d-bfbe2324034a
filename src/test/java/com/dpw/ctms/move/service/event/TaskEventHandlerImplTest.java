package com.dpw.ctms.move.service.event;

import com.dpw.ctms.move.dto.producer.EventRequestDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.request.message.TaskStatusUpdateMessage;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.IEventProcessorService;
import com.dpw.ctms.move.service.event.impl.TaskEventHandlerImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TaskEventHandlerImplTest {

    @Mock
    private IEventProcessorService<TaskStatusUpdateMessage> eventProcessorService;

    @Mock
    private IEntityEventManager entityEventManager;

    @InjectMocks
    private TaskEventHandlerImpl taskEventHandler;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(taskEventHandler, "taskStatusUpdateTopicName", "test-task-topic");
    }

    @Test
    void register_ShouldRegisterWithEntityEventManager() {
        taskEventHandler.register();
        verify(entityEventManager).register(eq(StateMachineEntityType.TASK.name()), eq(taskEventHandler));
    }

    @Test
    void updateStatusEvent_ShouldProcessEventSuccessfully() {
        EventRequestDTO<Task> eventRequest = Fakers.createDefaultTaskEventRequestDTO();
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("TASK_STATUS_UPDATE"), any())).thenReturn(mockResponse);
        
        IntegratorMessageResponse result = taskEventHandler.updateStatusEvent(eventRequest);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("TASK_STATUS_UPDATE"), any());
    }

    @Test
    void updateStatusEvent_WithCompleteData_ShouldIncludeAllFields() {
        EventRequestDTO<Task> eventRequest = Fakers.createTaskEventRequestDTO(
                "TASK001", TaskStatus.CREATED, TaskStatus.COMPLETED);
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("TASK_STATUS_UPDATE"), any())).thenReturn(mockResponse);

        IntegratorMessageResponse result = taskEventHandler.updateStatusEvent(eventRequest);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("TASK_STATUS_UPDATE"), any());
    }

    @Test
    void updateStatusEvent_WhenExceptionOccurs_ShouldReturnEmptyResponse() {
        EventRequestDTO<Task> eventRequest = Fakers.createDefaultTaskEventRequestDTO();
        doThrow(new RuntimeException("Test exception")).when(eventProcessorService).processRequest(any(), any());
        
        IntegratorMessageResponse result = taskEventHandler.updateStatusEvent(eventRequest);
        
        assertNotNull(result);
        assertFalse(result.isSuccess()); // Empty builder creates response with success = false
        verify(eventProcessorService).processRequest(any(), any());
    }

    @Test
    void integrationTest_MultipleTasks_ShouldProcessAllCorrectly() {
        taskEventHandler.register();
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("TASK_STATUS_UPDATE"), any())).thenReturn(mockResponse);
        
        for (int i = 1; i <= 3; i++) {
            String taskCode = "TASK" + String.format("%03d", i);
            EventRequestDTO<Task> eventRequest = Fakers.createTaskEventRequestDTO(
                    taskCode, TaskStatus.CREATED, TaskStatus.COMPLETED);
            IntegratorMessageResponse result = taskEventHandler.updateStatusEvent(eventRequest);
            
            assertNotNull(result);
            assertTrue(result.isSuccess());
        }

        verify(eventProcessorService, times(3)).processRequest(eq("TASK_STATUS_UPDATE"), any());
    }

    @Test
    void updateStatusEvent_WithEventTypeAndComments_ShouldIncludeInMessage() {
        EventRequestDTO<Task> eventRequest = Fakers.createTaskEventRequestDTO(
                "TASK001", TaskStatus.CREATED, TaskStatus.COMPLETED);
        eventRequest.setEventType("MANUAL_UPDATE");
        eventRequest.setComments("Manual task completion");
        
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("TASK_STATUS_UPDATE"), any())).thenReturn(mockResponse);

        IntegratorMessageResponse result = taskEventHandler.updateStatusEvent(eventRequest);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("TASK_STATUS_UPDATE"), any());
    }

    @Test
    void updateStatusEvent_WithTaskRegistrationCode_ShouldIncludeInMessage() {
        EventRequestDTO<Task> eventRequest = Fakers.createTaskEventRequestDTO(
                "TASK001", TaskStatus.CREATED, TaskStatus.COMPLETED);
        
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("TASK_STATUS_UPDATE"), any())).thenReturn(mockResponse);

        IntegratorMessageResponse result = taskEventHandler.updateStatusEvent(eventRequest);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("TASK_STATUS_UPDATE"), any());
    }
}