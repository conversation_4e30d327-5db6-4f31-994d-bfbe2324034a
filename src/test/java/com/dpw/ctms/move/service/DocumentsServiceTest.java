package com.dpw.ctms.move.service;

import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.enums.*;
import com.dpw.ctms.move.mapper.DocumentMapper;
import com.dpw.ctms.move.repository.DocumentRepository;
import com.dpw.ctms.move.request.EntityDocumentRequest;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import com.dpw.ctms.move.response.DocumentDownloadResponse;
import com.dpw.ctms.move.response.DocumentErrorResponse;
import com.dpw.ctms.move.response.EntityDocumentResponse;
import com.dpw.ctms.move.response.FileDownloadPreSignedUrlResponse;
import com.dpw.ctms.move.response.PreSignedUrlResponse;
import com.dpw.ctms.move.service.document.DocumentGenerator;
import com.dpw.ctms.move.service.document.DocumentGeneratorFactory;
import com.dpw.ctms.move.service.impl.DocumentsServiceImpl;
import com.dpw.ctms.move.util.CanonicalChecksum;
import com.dpw.ctms.move.util.DocumentUtil;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.schemaobjects.*;
import com.dpw.tmsutils.service.DocumentService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.data.jpa.domain.Specification;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentsServiceTest {

    @Mock
    private DocumentGeneratorFactory documentGeneratorFactory;

    @Mock
    private DocumentRepository documentRepository;

    @Mock
    private CanonicalChecksum canonicalChecksum;

    @Mock
    private DocumentService documentService;

    @Mock
    private ConfigService configService;

    @Mock
    private DocumentMapper documentMapper;

    @Mock
    private DocumentGenerator documentGenerator;

    @InjectMocks
    private DocumentsServiceImpl documentsService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(documentsService, "writeCount", 100);
    }

    @Test
    void downloadTripBolDocument_shouldGenerateNewDocumentWhenNotExists() {
        // Given
        String tripCode = "TRIP123";
        Tenant tenant = Tenant.CFR;
        Object jsonObject = Map.of("tripCode", "TRIP123");
        String checksum = "checksum123";
        String fileIdentifier = "file123";
        String presignedUrl = "https://example.com/download";

        when(documentGeneratorFactory.getGenerator(DocumentType.BOL)).thenReturn(documentGenerator);
        when(documentGenerator.generateJson(tripCode, tenant)).thenReturn(jsonObject);
        when(canonicalChecksum.generateChecksum(jsonObject)).thenReturn(checksum);
        when(documentRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.empty());

        JsonNode config = objectMapper.createObjectNode().put("templateId", "template123");
        when(configService.getConfig(any(), eq(tenant))).thenReturn(config);

        PrintBolResponse printBolResponse = new PrintBolResponse();
        printBolResponse.setFileIdentifier(fileIdentifier);
        printBolResponse.setPresignedDownloadUrl(presignedUrl);

        DocumentServiceResponse<PrintBolResponse> response = new DocumentServiceResponse<>();
        response.setData(printBolResponse);

        when(documentService.getBol(any(PrintBolRequest.class), eq("template123"))).thenReturn(response);

        // Mock the documentMapper.toEntity to return a Document
        Document mockDocument = Document.builder()
                .entityId(tripCode)
                .entityType(EntityType.TRIP.name())
                .documentType(DocumentType.BOL)
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.DOWNLOAD)
                .checksum(checksum)
                .build();
        when(documentMapper.toEntity(any())).thenReturn(mockDocument);

        // When
        DocumentDownloadResponse result = documentsService.downloadTripBolDocument(tripCode, tenant);

        // Then
        assertNotNull(result);
        assertEquals(presignedUrl, result.getPresignedDownloadUrl());

        verify(documentRepository).save(any(Document.class));
        verify(documentService).getBol(any(PrintBolRequest.class), eq("template123"));
    }

    @Test
    void downloadTripBolDocument_shouldReturnExistingDocumentPresignedUrl() {
        // Given
        String tripCode = "TRIP123";
        Tenant tenant = Tenant.CFR;
        Object jsonObject = Map.of("tripCode", "TRIP123");
        String checksum = "checksum123";
        String fileIdentifier = UUID.randomUUID().toString();
        String newPresignedUrl = "https://example.com/new-download";

        Document existingDocument = Document.builder()
                .fileIdentifier(fileIdentifier)
                .build();

        when(documentGeneratorFactory.getGenerator(DocumentType.BOL)).thenReturn(documentGenerator);
        when(documentGenerator.generateJson(tripCode, tenant)).thenReturn(jsonObject);
        when(canonicalChecksum.generateChecksum(jsonObject)).thenReturn(checksum);
        when(documentRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(existingDocument));

        DownloadPreSignedURLResponse downloadResponse = DownloadPreSignedURLResponse.builder()
                .preSignedUrl(newPresignedUrl)
                .build();

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> response = new DocumentServiceResponse<>();
        response.setData(Collections.singletonList(downloadResponse));

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(response);

        // When
        DocumentDownloadResponse result = documentsService.downloadTripBolDocument(tripCode, tenant);

        // Then
        assertNotNull(result);
        assertEquals(newPresignedUrl, result.getPresignedDownloadUrl());

        verify(documentRepository, never()).save(any(Document.class));
        verify(documentService, never()).getBol(any(PrintBolRequest.class), anyString());
        verify(documentService).getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class));
    }

    @Test
    void getPreSignedUrl_shouldReturnSuccessfully() {
        // Given
        String clientIdentifier = "client123";
        String presignedUrl = "https://example.com/upload";

        GetPreSignedURLResponse getPreSignedURLResponse = GetPreSignedURLResponse.builder()
                .preSignedUrl(presignedUrl)
                .build();

        DocumentServiceResponse<GetPreSignedURLResponse> response = new DocumentServiceResponse<>();
        response.setData(getPreSignedURLResponse);
        response.setError(false);

        when(documentService.getPreSignedURL(any(GetPreSignedURLRequest.class))).thenReturn(response);

        // When
        PreSignedUrlResponse result = documentsService.getPreSignedUrl();

        // Then
        assertNotNull(result);
        assertEquals(presignedUrl, result.getPreSignedURL());
        assertNotNull(result.getClientIdentifier());

        verify(documentService).getPreSignedURL(any(GetPreSignedURLRequest.class));
    }

    @Test
    void getPreSignedUrl_shouldThrowExceptionWhenServiceFails() {
        // Given
        DocumentServiceResponse<GetPreSignedURLResponse> response = new DocumentServiceResponse<>();
        response.setError(true);
        response.setErrorDescription("Service unavailable");

        when(documentService.getPreSignedURL(any(GetPreSignedURLRequest.class))).thenReturn(response);

        // When & Then
        assertThrows(TMSException.class, () -> documentsService.getPreSignedUrl());

        verify(documentService).getPreSignedURL(any(GetPreSignedURLRequest.class));
    }

    @Test
    void findAndUpdate_PreSignedUrlEvent_shouldUpdateInactiveDocument() {
        // Given
        String fileIdentifier = "file123";
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileIdentifier(fileIdentifier);

        Document inactiveDocument = Document.builder()
                .fileIdentifier(fileIdentifier)
                .status(DocumentStatus.INACTIVE)
                .build();

        when(documentRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(inactiveDocument));
        when(documentRepository.save(any(Document.class))).thenReturn(inactiveDocument);

        // When
        documentsService.findAndUpdate(event);

        // Then
        verify(documentMapper).updateDocument(inactiveDocument, event);
        verify(documentRepository).save(inactiveDocument);
        assertEquals(DocumentStatus.ACTIVE, inactiveDocument.getStatus());
    }

    @Test
    void findAndUpdate_PreSignedUrlEvent_shouldNotUpdateActiveDocument() {
        // Given
        String fileIdentifier = "file123";
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileIdentifier(fileIdentifier);

        Document activeDocument = Document.builder()
                .fileIdentifier(fileIdentifier)
                .status(DocumentStatus.ACTIVE)
                .build();

        when(documentRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(activeDocument));

        // When
        documentsService.findAndUpdate(event);

        // Then
        verify(documentMapper, never()).updateDocument(any(Document.class), any(PreSignedUrlEvent.class));
        verify(documentRepository, never()).save(any(Document.class));
    }

    @Test
    void findAndUpdate_PreSignedUrlEvent_shouldCreateNewDocumentWhenNotExists() {
        // Given
        String fileIdentifier = "file123";
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileIdentifier(fileIdentifier);

        Document newDocument = Document.builder()
                .fileIdentifier(fileIdentifier)
                .status(DocumentStatus.INACTIVE)
                .build();

        when(documentRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.empty());
        when(documentMapper.createDocument(event)).thenReturn(newDocument);
        when(documentRepository.save(newDocument)).thenReturn(newDocument);

        // When
        documentsService.findAndUpdate(event);

        // Then
        verify(documentMapper).createDocument(event);
        verify(documentRepository).save(newDocument);
    }

    @Test
    void findAndUpdate_DeliveryTaskDocumentDTO_shouldActivateDocumentAndDiscardOthers() {
        // Given
        String fileIdentifier = "file123";
        String entityId = "entity123";
        EntityType entityType = EntityType.SHIPMENT;
        DocumentOperationType operationType = DocumentOperationType.UPLOAD;

        DeliveryTaskDocumentDTO kafkaEvent = new DeliveryTaskDocumentDTO();
        kafkaEvent.setAsyncMappingUUID(fileIdentifier);
        kafkaEvent.setEntityId(entityId);
        kafkaEvent.setEntityType(entityType.name());
        kafkaEvent.setOperationType(operationType);

        Document inactiveDocument = Document.builder()
                .asyncMappingUUID(fileIdentifier)
                .status(DocumentStatus.INACTIVE)
                .build();

        Document activeDoc1 = Document.builder()
                .asyncMappingUUID("other1")
                .status(DocumentStatus.ACTIVE)
                .build();

        Document activeDoc2 = Document.builder()
                .asyncMappingUUID("other2")
                .status(DocumentStatus.ACTIVE)
                .build();

        when(documentRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(inactiveDocument));
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Arrays.asList(activeDoc1, activeDoc2));
        when(documentRepository.save(any(Document.class))).thenReturn(inactiveDocument);

        // When
        documentsService.findAndUpdate(kafkaEvent);

        // Then
        verify(documentMapper).updateDocument(inactiveDocument, kafkaEvent);
        verify(documentRepository).save(inactiveDocument);
        verify(documentRepository).saveAll(Arrays.asList(activeDoc1, activeDoc2));
        
        assertEquals(DocumentStatus.ACTIVE, inactiveDocument.getStatus());
        assertEquals(DocumentStatus.DISCARDED, activeDoc1.getStatus());
        assertEquals(DocumentStatus.DISCARDED, activeDoc2.getStatus());
    }

    @Test
    void getAllErrors_shouldReturnErrorsForInactiveDocuments() {
        // Given
        List<String> fileIdentifiers = Arrays.asList("file1", "file2", "file3");

        Document activeDoc = Document.builder()
                .asyncMappingUUID("file1")
                .status(DocumentStatus.ACTIVE)
                .build();

        Document inactiveDoc = Document.builder()
                .asyncMappingUUID("file2")
                .status(DocumentStatus.INACTIVE)
                .fileName("test.pdf")
                .fileSize(1024)
                .build();

        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Arrays.asList(activeDoc, inactiveDoc));

        try (MockedStatic<DocumentUtil> documentUtilMock = mockStatic(DocumentUtil.class)) {
            documentUtilMock.when(() -> DocumentUtil.isDocumentInactive(activeDoc)).thenReturn(false);
            documentUtilMock.when(() -> DocumentUtil.isDocumentInactive(inactiveDoc)).thenReturn(true);

            // When
            DocumentErrorResponse result = documentsService.getAllErrors(fileIdentifiers);

            // Then
            assertNotNull(result);
            assertNotNull(result.getFailedDocumentDetails());
            assertEquals(2, result.getFailedDocumentDetails().size());

            DocumentErrorResponse.FileErrorDetails errorDetail = result.getFailedDocumentDetails().get(1);
            assertEquals("file2", errorDetail.getAsyncMappingUUID());
            assertEquals("test.pdf", errorDetail.getFileName());
            assertEquals(1024, errorDetail.getFileSize());
        }
    }

    @Test
    void getAllErrors_shouldReturnEmptyWhenAllDocumentsActive() {
        // Given
        List<String> asyncMappingUUID = Arrays.asList("file1", "file2");

        Document activeDoc1 = Document.builder()
                .asyncMappingUUID("file1")
                .status(DocumentStatus.ACTIVE)
                .build();

        Document activeDoc2 = Document.builder()
                .asyncMappingUUID("file2")
                .status(DocumentStatus.ACTIVE)
                .build();

        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Arrays.asList(activeDoc1, activeDoc2));

        try (MockedStatic<DocumentUtil> documentUtilMock = mockStatic(DocumentUtil.class)) {
            documentUtilMock.when(() -> DocumentUtil.isDocumentInactive(any(Document.class))).thenReturn(false);

            // When
            DocumentErrorResponse result = documentsService.getAllErrors(asyncMappingUUID);

            // Then
            assertNotNull(result);
            assertNull(result.getFailedDocumentDetails());
        }
    }

    @Test
    void getAllErrors_shouldReturnEmptyWhenNoDocumentsFound() {
        // Given
        List<String> fileIdentifiers = Arrays.asList("file1", "file2");

        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());

        // When
        DocumentErrorResponse result = documentsService.getAllErrors(fileIdentifiers);

        // Then
        assertNotNull(result);
        assertNull(result.getFailedDocumentDetails());
    }

    @Test
    void getAllErrors_shouldHandleEmptyFileIdentifiersList() {
        // Given
        List<String> emptyList = Collections.emptyList();

        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());

        // When
        DocumentErrorResponse result = documentsService.getAllErrors(emptyList);

        // Then
        assertNotNull(result);
        assertNull(result.getFailedDocumentDetails());
    }

    @Test
    void getDocumentsByEntity_shouldReturnDocumentsForMultipleEntities() {
        // Given
        List<EntityDocumentRequest> entityRequests = Arrays.asList(
                EntityDocumentRequest.builder()
                        .entityCode("TASK-1234")
                        .entityType("TASK")
                        .build(),
                EntityDocumentRequest.builder()
                        .entityCode("TASK-5678")
                        .entityType("TASK")
                        .build()
        );

        Document doc1 = Document.builder()
                .entityId("TASK-1234")
                .entityType("TASK")
                .asyncMappingUUID("uuid1")
                .fileName("doc1.pdf")
                .fileSize(1024)
                .fileType("application/pdf")
                .fileIdentifier("file-id-1")
                .status(DocumentStatus.ACTIVE)
                .build();

        Document doc2 = Document.builder()
                .entityId("TASK-5678")
                .entityType("TASK")
                .asyncMappingUUID("uuid2")
                .fileName("doc2.pdf")
                .fileSize(2048)
                .fileType("application/pdf")
                .fileIdentifier("file-id-2")
                .status(DocumentStatus.ACTIVE)
                .build();

        EntityDocumentResponse.FileDetail fileDetail1 = EntityDocumentResponse.FileDetail.builder()
                .entityType("TASK")
                .entityCode("TASK-1234")
                .asyncMappingUUID("uuid1")
                .fileName("doc1.pdf")
                .fileSize(1024)
                .fileType("application/pdf")
                .externalDocumentIdentifier("file-id-1")
                .build();

        EntityDocumentResponse.FileDetail fileDetail2 = EntityDocumentResponse.FileDetail.builder()
                .entityType("TASK")
                .entityCode("TASK-5678")
                .asyncMappingUUID("uuid2")
                .fileName("doc2.pdf")
                .fileSize(2048)
                .fileType("application/pdf")
                .externalDocumentIdentifier("file-id-2")
                .build();

        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.singletonList(doc1))
                .thenReturn(Collections.singletonList(doc2));
        when(documentMapper.toFileDetail(doc1)).thenReturn(fileDetail1);
        when(documentMapper.toFileDetail(doc2)).thenReturn(fileDetail2);

        // When
        EntityDocumentResponse response = documentsService.getDocumentsByEntity(entityRequests);

        // Then
        assertNotNull(response);
        assertNotNull(response.getData());
        assertNotNull(response.getData().getFileDetails());
        assertEquals(2, response.getData().getFileDetails().size());
        assertTrue(response.getData().getFileDetails().contains(fileDetail1));
        assertTrue(response.getData().getFileDetails().contains(fileDetail2));
        verify(documentRepository, times(2)).findAll(any(Specification.class));
    }

    @Test
    void getDocumentsByEntity_shouldReturnEmptyListWhenNoDocumentsFound() {
        // Given
        List<EntityDocumentRequest> entityRequests = Collections.singletonList(
                EntityDocumentRequest.builder()
                        .entityCode("TASK-9999")
                        .entityType("TASK")
                        .build()
        );

        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());

        // When
        EntityDocumentResponse response = documentsService.getDocumentsByEntity(entityRequests);

        // Then
        assertNotNull(response);
        assertNotNull(response.getData());
        assertNotNull(response.getData().getFileDetails());
        assertTrue(response.getData().getFileDetails().isEmpty());
    }

    @Test
    void getFileDownloadUrls_shouldReturnPresignedUrlsForValidIdentifiers() {
        // Given
        String fileId1 = "550e8400-e29b-41d4-a716-************";
        String fileId2 = "550e8400-e29b-41d4-a716-************";
        List<String> externalDocumentIdentifiers = Arrays.asList(fileId1, fileId2);

        List<DownloadPreSignedURLResponse> downloadResponses = Arrays.asList(
                DownloadPreSignedURLResponse.builder()
                        .fileIdentifier(fileId1)
                        .preSignedUrl("https://example.com/download1")
                        .build(),
                DownloadPreSignedURLResponse.builder()
                        .fileIdentifier(fileId2)
                        .preSignedUrl("https://example.com/download2")
                        .build()
        );

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> serviceResponse = 
                DocumentServiceResponse.<List<DownloadPreSignedURLResponse>>builder()
                        .data(downloadResponses)
                        .error(false)
                        .build();

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(serviceResponse);

        // When
        FileDownloadPreSignedUrlResponse response = documentsService.getFileDownloadUrls(externalDocumentIdentifiers);

        // Then
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(2, response.getData().size());
        assertEquals("https://example.com/download1", response.getData().get(fileId1));
        assertEquals("https://example.com/download2", response.getData().get(fileId2));
    }

    @Test
    void getFileDownloadUrls_shouldThrowExceptionForInvalidUUIDFormat() {
        // Given
        List<String> externalDocumentIdentifiers = Arrays.asList("invalid-uuid", "550e8400-e29b-41d4-a716-************");

        // When & Then
        TMSException exception = assertThrows(TMSException.class, () -> 
                documentsService.getFileDownloadUrls(externalDocumentIdentifiers));
        
        assertTrue(exception.getErrorMessage().contains("Invalid file identifier format: invalid-uuid"));
        verifyNoInteractions(documentService);
    }

    @Test
    void getFileDownloadUrls_shouldThrowExceptionWhenDocumentServiceReturnsError() {
        // Given
        String fileId = "550e8400-e29b-41d4-a716-************";
        List<String> externalDocumentIdentifiers = Collections.singletonList(fileId);

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> errorResponse = 
                DocumentServiceResponse.<List<DownloadPreSignedURLResponse>>builder()
                        .error(true)
                        .errorDescription("Document service error")
                        .build();

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(errorResponse);

        // When & Then
        TMSException exception = assertThrows(TMSException.class, () -> 
                documentsService.getFileDownloadUrls(externalDocumentIdentifiers));
        
        assertEquals("Document service error", exception.getErrorMessage());
    }

    @Test
    void getFileDownloadUrls_shouldHandleEmptyList() {
        // Given
        List<String> emptyList = Collections.emptyList();

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> serviceResponse = 
                DocumentServiceResponse.<List<DownloadPreSignedURLResponse>>builder()
                        .data(Collections.emptyList())
                        .error(false)
                        .build();

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(serviceResponse);

        // When
        FileDownloadPreSignedUrlResponse response = documentsService.getFileDownloadUrls(emptyList);

        // Then
        assertNotNull(response);
        assertNotNull(response.getData());
        assertTrue(response.getData().isEmpty());
    }
}