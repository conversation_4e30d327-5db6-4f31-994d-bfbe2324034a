package com.dpw.ctms.move.service.event;

import com.dpw.ctms.move.dto.producer.EventRequestDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.event.impl.EntityEventManagerImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EntityEventManagerImplTest {

    private EntityEventManagerImpl entityEventManager;

    @Mock
    private IEntityEventHandler<Shipment> shipmentEventHandler;

    @Mock
    private IEntityEventHandler<Task> taskEventHandler;

    @Mock
    private IEntityEventHandler<Trip> tripEventHandler;

    @BeforeEach
    void setUp() {
        entityEventManager = new EntityEventManagerImpl();
    }

    @Test
    void register_AndUpdateStatus_ShouldProcessEvent() {
        String entityType = StateMachineEntityType.SHIPMENT.name();
        entityEventManager.register(entityType, shipmentEventHandler);
        EventRequestDTO<Shipment> request = createShipmentEventRequest(entityType);
        
        when(shipmentEventHandler.updateStatusEvent(request))
                .thenReturn(IntegratorMessageResponse.builder().success(true).build());

        boolean result = entityEventManager.updateStatus(request);

        assertTrue(result);
        verify(shipmentEventHandler).updateStatusEvent(request);
    }

    @Test
    void updateStatus_WithUnregisteredHandler_ShouldReturnFalse() {
        EventRequestDTO<Shipment> request = createShipmentEventRequest("UNKNOWN");

        boolean result = entityEventManager.updateStatus(request);

        assertFalse(result);
        verify(shipmentEventHandler, never()).updateStatusEvent(any());
    }

    @Test
    void register_WithNullHandler_ShouldHandleGracefully() {
        String entityType = StateMachineEntityType.SHIPMENT.name();
        entityEventManager.register(entityType, null);
        EventRequestDTO<Shipment> request = createShipmentEventRequest(entityType);

        boolean result = entityEventManager.updateStatus(request);

        assertFalse(result);
    }

    @Test
    void updateStatus_WithNullRequest_ShouldReturnFalse() {
        boolean result = entityEventManager.updateStatus(null);

        assertFalse(result);
        verify(shipmentEventHandler, never()).updateStatusEvent(any());
    }

    @Test
    void updateStatus_WithEmptyEntityType_ShouldReturnFalse() {
        EventRequestDTO<Shipment> request = createShipmentEventRequest("");

        boolean result = entityEventManager.updateStatus(request);

        assertFalse(result);
        verify(shipmentEventHandler, never()).updateStatusEvent(any());
    }

    @Test
    void updateStatus_WithNullEntitiesInRequest_ShouldStillProcess() {
        String entityType = StateMachineEntityType.SHIPMENT.name();
        entityEventManager.register(entityType, shipmentEventHandler);

        EventRequestDTO<Shipment> request = EventRequestDTO.<Shipment>builder()
                .entityType(entityType)
                .originalEntity(null)
                .updatedEntity(null)
                .eventType("STATUS_UPDATE")
                .build();

        when(shipmentEventHandler.updateStatusEvent(request))
                .thenReturn(IntegratorMessageResponse.builder().success(true).build());

        boolean result = entityEventManager.updateStatus(request);

        assertTrue(result);
        verify(shipmentEventHandler).updateStatusEvent(request);
    }

    @Test
    void register_MultipleHandlers_ShouldRegisterAllCorrectly() {
        String shipmentType = StateMachineEntityType.SHIPMENT.name();
        String taskType = StateMachineEntityType.TASK.name();
        String tripType = StateMachineEntityType.TRIP.name();

        entityEventManager.register(shipmentType, shipmentEventHandler);
        entityEventManager.register(taskType, taskEventHandler);
        entityEventManager.register(tripType, tripEventHandler);

        EventRequestDTO<Shipment> shipmentRequest = createShipmentEventRequest(shipmentType);
        EventRequestDTO<Task> taskRequest = createTaskEventRequest(taskType);
        EventRequestDTO<Trip> tripRequest = createTripEventRequest(tripType);

        when(shipmentEventHandler.updateStatusEvent(shipmentRequest))
                .thenReturn(IntegratorMessageResponse.builder().success(true).build());
        when(taskEventHandler.updateStatusEvent(taskRequest))
                .thenReturn(IntegratorMessageResponse.builder().success(true).build());
        when(tripEventHandler.updateStatusEvent(tripRequest))
                .thenReturn(IntegratorMessageResponse.builder().success(true).build());

        assertTrue(entityEventManager.updateStatus(shipmentRequest));
        assertTrue(entityEventManager.updateStatus(taskRequest));
        assertTrue(entityEventManager.updateStatus(tripRequest));

        verify(shipmentEventHandler).updateStatusEvent(shipmentRequest);
        verify(taskEventHandler).updateStatusEvent(taskRequest);
        verify(tripEventHandler).updateStatusEvent(tripRequest);
    }

    @Test
    void register_SameEntityTypeTwice_ShouldOverrideHandler() {
        String entityType = StateMachineEntityType.SHIPMENT.name();
        IEntityEventHandler<Shipment> firstHandler = mock(IEntityEventHandler.class);
        IEntityEventHandler<Shipment> secondHandler = mock(IEntityEventHandler.class);

        entityEventManager.register(entityType, firstHandler);
        entityEventManager.register(entityType, secondHandler);

        EventRequestDTO<Shipment> request = createShipmentEventRequest(entityType);
        when(secondHandler.updateStatusEvent(request))
                .thenReturn(IntegratorMessageResponse.builder().success(true).build());
        
        entityEventManager.updateStatus(request);

        verify(firstHandler, never()).updateStatusEvent(any());
        verify(secondHandler).updateStatusEvent(request);
    }

    @Test
    void updateStatus_WithMultipleCallsToSameHandler_ShouldProcessAll() {
        String entityType = StateMachineEntityType.SHIPMENT.name();
        entityEventManager.register(entityType, shipmentEventHandler);

        EventRequestDTO<Shipment> request1 = createShipmentEventRequest(entityType);
        EventRequestDTO<Shipment> request2 = createShipmentEventRequest(entityType);

        when(shipmentEventHandler.updateStatusEvent(any()))
                .thenReturn(IntegratorMessageResponse.builder().success(true).build());

        boolean result1 = entityEventManager.updateStatus(request1);
        boolean result2 = entityEventManager.updateStatus(request2);

        assertTrue(result1);
        assertTrue(result2);
        verify(shipmentEventHandler).updateStatusEvent(request1);
        verify(shipmentEventHandler).updateStatusEvent(request2);
    }

    @Test
    void updateStatus_WithDifferentEventTypes_ShouldProcessAllCorrectly() {
        String entityType = StateMachineEntityType.SHIPMENT.name();
        entityEventManager.register(entityType, shipmentEventHandler);

        EventRequestDTO<Shipment> statusUpdateRequest = EventRequestDTO.<Shipment>builder()
                .entityType(entityType)
                .originalEntity(createShipment("SHP001", ShipmentStatus.IN_TRANSIT))
                .updatedEntity(createShipment("SHP001", ShipmentStatus.CANCELLED))
                .eventType("STATUS_UPDATE")
                .build();

        EventRequestDTO<Shipment> manualUpdateRequest = EventRequestDTO.<Shipment>builder()
                .entityType(entityType)
                .originalEntity(createShipment("SHP002", ShipmentStatus.IN_TRANSIT))
                .updatedEntity(createShipment("SHP002", ShipmentStatus.CANCELLED))
                .eventType("MANUAL_UPDATE")
                .build();

        when(shipmentEventHandler.updateStatusEvent(any()))
                .thenReturn(IntegratorMessageResponse.builder().success(true).build());

        assertTrue(entityEventManager.updateStatus(statusUpdateRequest));
        assertTrue(entityEventManager.updateStatus(manualUpdateRequest));

        verify(shipmentEventHandler).updateStatusEvent(statusUpdateRequest);
        verify(shipmentEventHandler).updateStatusEvent(manualUpdateRequest);
    }

    @Test
    void updateStatus_WhenHandlerReturnsFailure_ShouldReturnFalse() {
        String entityType = StateMachineEntityType.SHIPMENT.name();
        entityEventManager.register(entityType, shipmentEventHandler);
        EventRequestDTO<Shipment> request = createShipmentEventRequest(entityType);

        when(shipmentEventHandler.updateStatusEvent(request))
                .thenReturn(IntegratorMessageResponse.builder().success(false).build());

        boolean result = entityEventManager.updateStatus(request);

        assertFalse(result);
        verify(shipmentEventHandler).updateStatusEvent(request);
    }

    @Test
    void updateStatus_WhenHandlerReturnsNull_ShouldReturnFalse() {
        String entityType = StateMachineEntityType.SHIPMENT.name();
        entityEventManager.register(entityType, shipmentEventHandler);
        EventRequestDTO<Shipment> request = createShipmentEventRequest(entityType);

        when(shipmentEventHandler.updateStatusEvent(request)).thenReturn(null);

        boolean result = entityEventManager.updateStatus(request);

        assertFalse(result);
        verify(shipmentEventHandler).updateStatusEvent(request);
    }

    @Test
    void updateStatus_WhenHandlerThrowsException_ShouldReturnFalse() {
        String entityType = StateMachineEntityType.SHIPMENT.name();
        entityEventManager.register(entityType, shipmentEventHandler);
        EventRequestDTO<Shipment> request = createShipmentEventRequest(entityType);

        when(shipmentEventHandler.updateStatusEvent(request))
                .thenThrow(new RuntimeException("Handler failed"));

        boolean result = entityEventManager.updateStatus(request);

        assertFalse(result);
        verify(shipmentEventHandler).updateStatusEvent(request);
    }

    private EventRequestDTO<Shipment> createShipmentEventRequest(String entityType) {
        return EventRequestDTO.<Shipment>builder()
                .entityType(entityType)
                .originalEntity(createShipment("SHP001", ShipmentStatus.ASSIGNED))
                .updatedEntity(createShipment("SHP001", ShipmentStatus.ALLOCATED))
                .eventType("STATUS_UPDATE")
                .build();
    }

    private EventRequestDTO<Task> createTaskEventRequest(String entityType) {
        return EventRequestDTO.<Task>builder()
                .entityType(entityType)
                .originalEntity(createTask("TASK001", TaskStatus.CREATED))
                .updatedEntity(createTask("TASK001", TaskStatus.COMPLETED))
                .eventType("STATUS_UPDATE")
                .build();
    }

    private EventRequestDTO<Trip> createTripEventRequest(String entityType) {
        return EventRequestDTO.<Trip>builder()
                .entityType(entityType)
                .originalEntity(createTrip("TRIP001", TripStatus.CREATED))
                .updatedEntity(createTrip("TRIP001", TripStatus.COMPLETED))
                .eventType("STATUS_UPDATE")
                .build();
    }

    private Shipment createShipment(String code, ShipmentStatus status) {
        Shipment shipment = new Shipment();
        shipment.setCode(code);
        shipment.setStatus(status);
        return shipment;
    }

    private Task createTask(String code, TaskStatus status) {
        Task task = new Task();
        task.setCode(code);
        task.setStatus(status);
        return task;
    }

    private Trip createTrip(String code, TripStatus status) {
        Trip trip = new Trip();
        trip.setCode(code);
        trip.setStatus(status);
        return trip;
    }
}