package com.dpw.ctms.move.service.event;

import com.dpw.ctms.move.dto.producer.ShipmentCancellationEventRequestDTO;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.event.impl.EntityCancellationEventManagerImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;


import static org.mockito.Mockito.*;

class EntityCancellationEventManagerImplTest {

    private EntityCancellationEventManagerImpl eventManager;
    private IEntityCancellationEventHandler mockHandler;

    @BeforeEach
    void setUp() {
        eventManager = new EntityCancellationEventManagerImpl();
        mockHandler = mock(IEntityCancellationEventHandler.class);
    }

    @Test
    void shouldLogError_whenHandlerNotRegistered() {
        ShipmentCancellationEventRequestDTO dto = new ShipmentCancellationEventRequestDTO();
        dto.setEntityType("UNREGISTERED_ENTITY");

        eventManager.acknowledgeEntityCancellation(dto);
        // No exception should be thrown; just logs the error
    }

    @Test
    void shouldProcessEventSuccessfully_whenHandlerIsRegistered() {
        ShipmentCancellationEventRequestDTO dto = new ShipmentCancellationEventRequestDTO();
        dto.setEntityType("SHIPMENT");

        IntegratorMessageResponse mockResponse = new IntegratorMessageResponse();
        mockResponse.setSuccess(true);

        when(mockHandler.acknowledgeEntityCancellationEvent(dto)).thenReturn(mockResponse);

        eventManager.register("SHIPMENT", mockHandler);
        eventManager.acknowledgeEntityCancellation(dto);

        verify(mockHandler, times(1)).acknowledgeEntityCancellationEvent(dto);
    }

    @Test
    void shouldLogError_whenHandlerThrowsException() {
        ShipmentCancellationEventRequestDTO dto = new ShipmentCancellationEventRequestDTO();
        dto.setEntityType("SHIPMENT");

        when(mockHandler.acknowledgeEntityCancellationEvent(dto)).thenThrow(new RuntimeException("Test Exception"));

        eventManager.register("SHIPMENT", mockHandler);
        eventManager.acknowledgeEntityCancellation(dto);

        verify(mockHandler).acknowledgeEntityCancellationEvent(dto);
    }
}

