package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.integration.adapter.ResourceServiceAdapter;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;
import com.dpw.ctms.move.integration.request.resource.GetOperatorListRequest;
import com.dpw.ctms.move.service.TaskParamService;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VehicleOperatorServiceImplTest {

    @Mock
    private ResourceServiceAdapter resourceServiceAdapter;

    @Mock
    private TaskParamService taskParamService;

    @InjectMocks
    private VehicleOperatorServiceImpl vehicleOperatorService;

    @Captor
    private ArgumentCaptor<GetOperatorListRequest> requestCaptor;

    private Map<String, List<String>> taskVehicleOperatorMap;
    private Map<String, GetOperatorDetailsListResponse> expectedOperatorDetailsMap;

    // Test data for populateCrpIdInTransportOrder tests
    private TransportOrder testTransportOrder;
    private List<Task> testTaskList;
    private Task testTask;
    private Trip testTrip;
    private VehicleOperatorResource testVehicleOperatorResource;
    private ParamValueVehicleOperatorDTO testVehicleOperatorDTO;
    private GetOperatorDetailsListResponse testOperatorDetails;

    @BeforeEach
    void setUp() {
        taskVehicleOperatorMap = new HashMap<>();
        expectedOperatorDetailsMap = new HashMap<>();

        // Setup test data for fetchOperatorDetails tests
        GetOperatorDetailsListResponse operatorDetails1 = new GetOperatorDetailsListResponse();
        GetOperatorDetailsListResponse operatorDetails2 = new GetOperatorDetailsListResponse();

        expectedOperatorDetailsMap.put("1", operatorDetails1);
        expectedOperatorDetailsMap.put("2", operatorDetails2);

        // Setup test data for populateCrpIdInTransportOrder tests
        setupPopulateCrpIdTestData();
    }

    private void setupPopulateCrpIdTestData() {
        // Create test task
        testTask = new Task();
        testTask.setId(1L);
        testTask.setCode("TASK001");

        testTaskList = Arrays.asList(testTask);

        // Create test vehicle operator DTO
        testVehicleOperatorDTO = new ParamValueVehicleOperatorDTO();
        testVehicleOperatorDTO.setExternalResourceId("1");

        // Create test vehicle operator resource
        testVehicleOperatorResource = new VehicleOperatorResource();
        testVehicleOperatorResource.setExternalResourceId("1");
        testVehicleOperatorResource.setCrpId(null);

        // Create test trip
        testTrip = new Trip();
        testTrip.setVehicleOperatorResources(Set.of(testVehicleOperatorResource));

        // Create test transport order
        testTransportOrder = new TransportOrder();
        testTransportOrder.setId(1L);
        testTransportOrder.setCode("TO001");
        testTransportOrder.setTrips(Set.of(testTrip));

        // Create test operator details with CRP details
        testOperatorDetails = new GetOperatorDetailsListResponse();
        testOperatorDetails.setId(1L);
        GetOperatorDetailsListResponse.CrpDetailsResponse crpDetails = new GetOperatorDetailsListResponse.CrpDetailsResponse();
        crpDetails.setCrpUserUUID("CRP001");
        testOperatorDetails.setCrpDetails(crpDetails);
    }

    @Test
    void fetchOperatorDetails_WithValidOperatorIds_ShouldReturnOperatorDetailsMap() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("1", "2"));
        taskVehicleOperatorMap.put("TASK002", Arrays.asList("2", "3"));

        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(expectedOperatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap);

        // Assert
        assertNotNull(result);
        assertEquals(expectedOperatorDetailsMap, result);

        verify(resourceServiceAdapter).getOperatorDetailsMap(requestCaptor.capture());

        GetOperatorListRequest capturedRequest = requestCaptor.getValue();
        assertNotNull(capturedRequest);
        assertNotNull(capturedRequest.getFilter());
        assertNotNull(capturedRequest.getFilter().getIds());
        assertEquals(3, capturedRequest.getFilter().getIds().size());
        assertTrue(capturedRequest.getFilter().getIds().containsAll(Arrays.asList(1L, 2L, 3L)));

        // Verify pagination
        assertNotNull(capturedRequest.getPagination());
        assertEquals(0, capturedRequest.getPagination().getPageNo());
        assertEquals(3, capturedRequest.getPagination().getPageSize());
    }

    @Test
    void fetchOperatorDetails_WithEmptyTaskVehicleOperatorMap_ShouldReturnEmptyMap() {
        // Arrange
        taskVehicleOperatorMap = Collections.emptyMap();

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verifyNoInteractions(resourceServiceAdapter);
    }

    @Test
    void fetchOperatorDetails_WithTasksHavingEmptyOperatorLists_ShouldReturnEmptyMap() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Collections.emptyList());
        taskVehicleOperatorMap.put("TASK002", Collections.emptyList());

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verifyNoInteractions(resourceServiceAdapter);
    }

    @Test
    void fetchOperatorDetails_WithDuplicateOperatorIds_ShouldDeduplicateIds() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("1", "2", "1"));
        taskVehicleOperatorMap.put("TASK002", Arrays.asList("2", "3", "2"));

        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(expectedOperatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap);

        // Assert
        assertNotNull(result);

        verify(resourceServiceAdapter).getOperatorDetailsMap(requestCaptor.capture());

        GetOperatorListRequest capturedRequest = requestCaptor.getValue();
        assertEquals(3, capturedRequest.getFilter().getIds().size());
        assertTrue(capturedRequest.getFilter().getIds().containsAll(Arrays.asList(1L, 2L, 3L)));
        assertEquals(3, capturedRequest.getPagination().getPageSize());
    }

    @Test
    void fetchOperatorDetails_WithSingleOperatorId_ShouldProcessCorrectly() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("123"));

        Map<String, GetOperatorDetailsListResponse> singleOperatorMap = new HashMap<>();
        singleOperatorMap.put("123", new GetOperatorDetailsListResponse());

        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(singleOperatorMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("123"));

        verify(resourceServiceAdapter).getOperatorDetailsMap(requestCaptor.capture());

        GetOperatorListRequest capturedRequest = requestCaptor.getValue();
        assertEquals(1, capturedRequest.getFilter().getIds().size());
        assertEquals(Long.valueOf(123), capturedRequest.getFilter().getIds().get(0));
        assertEquals(1, capturedRequest.getPagination().getPageSize());
    }

    @Test
    void fetchOperatorDetails_WithInvalidOperatorIdFormat_ShouldThrowTMSException() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("1", "invalid_id", "3"));

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () ->
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap));

        assertEquals("INVALID_OPERATOR_ID", exception.getErrorCode());
//        assertEquals("Invalid operator ID", exception.getMessage());

        verifyNoInteractions(resourceServiceAdapter);
    }

    @Test
    void fetchOperatorDetails_WithNonNumericOperatorId_ShouldThrowTMSException() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("abc"));

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () ->
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap));

        assertEquals("INVALID_OPERATOR_ID", exception.getErrorCode());
//        assertEquals("Invalid operator ID", exception.getMessage());

        verifyNoInteractions(resourceServiceAdapter);
    }

    @Test
    void fetchOperatorDetails_WithMixOfValidAndInvalidIds_ShouldThrowTMSException() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("1", "2"));
        taskVehicleOperatorMap.put("TASK002", Arrays.asList("3", "not_a_number"));

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () ->
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap));

        assertEquals("INVALID_OPERATOR_ID", exception.getErrorCode());
//        assertEquals("Invalid operator ID", exception.getMessage());

        verifyNoInteractions(resourceServiceAdapter);
    }

    @Test
    void fetchOperatorDetails_WithLargeNumberOperatorIds_ShouldProcessCorrectly() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("9223372036854775807", "1")); // Long.MAX_VALUE

        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(expectedOperatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap);

        // Assert
        assertNotNull(result);

        verify(resourceServiceAdapter).getOperatorDetailsMap(requestCaptor.capture());

        GetOperatorListRequest capturedRequest = requestCaptor.getValue();
        assertEquals(2, capturedRequest.getFilter().getIds().size());
        assertTrue(capturedRequest.getFilter().getIds().containsAll(
                Arrays.asList(Long.MAX_VALUE, 1L)));
    }

    @Test
    void fetchOperatorDetails_WithZeroOperatorId_ShouldProcessCorrectly() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("0"));

        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(expectedOperatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap);

        // Assert
        assertNotNull(result);

        verify(resourceServiceAdapter).getOperatorDetailsMap(requestCaptor.capture());

        GetOperatorListRequest capturedRequest = requestCaptor.getValue();
        assertEquals(1, capturedRequest.getFilter().getIds().size());
        assertEquals(Long.valueOf(0), capturedRequest.getFilter().getIds().get(0));
    }

    @Test
    void fetchOperatorDetails_WithNegativeOperatorId_ShouldProcessCorrectly() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("-1", "-100"));

        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(expectedOperatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap);

        // Assert
        assertNotNull(result);

        verify(resourceServiceAdapter).getOperatorDetailsMap(requestCaptor.capture());

        GetOperatorListRequest capturedRequest = requestCaptor.getValue();
        assertEquals(2, capturedRequest.getFilter().getIds().size());
        assertTrue(capturedRequest.getFilter().getIds().containsAll(Arrays.asList(-1L, -100L)));
    }

    @Test
    void fetchOperatorDetails_WhenResourceServiceAdapterThrowsException_ShouldPropagateException() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("1", "2"));

        RuntimeException serviceException = new RuntimeException("Service unavailable");
        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenThrow(serviceException);

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap));

        assertEquals("Service unavailable", exception.getMessage());
        verify(resourceServiceAdapter).getOperatorDetailsMap(any(GetOperatorListRequest.class));
    }

    @Test
    void fetchOperatorDetails_WithMultipleTasksAndOperators_ShouldCreateCorrectRequest() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("1", "2", "3"));
        taskVehicleOperatorMap.put("TASK002", Arrays.asList("4", "5"));
        taskVehicleOperatorMap.put("TASK003", Arrays.asList("6"));

        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(expectedOperatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap);

        // Assert
        verify(resourceServiceAdapter).getOperatorDetailsMap(requestCaptor.capture());

        GetOperatorListRequest capturedRequest = requestCaptor.getValue();

        // Verify all operator IDs are included
        assertEquals(6, capturedRequest.getFilter().getIds().size());
        assertTrue(capturedRequest.getFilter().getIds().containsAll(
                Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L)));

        // Verify pagination
        assertEquals(0, capturedRequest.getPagination().getPageNo());
        assertEquals(6, capturedRequest.getPagination().getPageSize());
    }

    @Test
    void fetchOperatorDetails_RequestBuilderVerification_ShouldBuildCorrectRequest() {
        // Arrange
        taskVehicleOperatorMap.put("TASK001", Arrays.asList("10", "20"));

        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(expectedOperatorDetailsMap);

        // Act
        vehicleOperatorService.fetchOperatorDetails(taskVehicleOperatorMap);

        // Assert
        verify(resourceServiceAdapter).getOperatorDetailsMap(requestCaptor.capture());

        GetOperatorListRequest capturedRequest = requestCaptor.getValue();

        // Verify request structure
        assertNotNull(capturedRequest);
        assertNotNull(capturedRequest.getFilter());
        assertNotNull(capturedRequest.getFilter().getIds());
        assertNotNull(capturedRequest.getPagination());

        // Verify filter contains correct IDs
        List<Long> expectedIds = Arrays.asList(10L, 20L);
        assertEquals(expectedIds.size(), capturedRequest.getFilter().getIds().size());
        assertTrue(capturedRequest.getFilter().getIds().containsAll(expectedIds));

        // Verify pagination settings
        assertEquals(0, capturedRequest.getPagination().getPageNo());
        assertEquals(expectedIds.size(), capturedRequest.getPagination().getPageSize());
    }

    // ===== Tests for populateCrpIdInTransportOrder method =====

    @Test
    void populateCrpIdInTransportOrder_WithValidData_ShouldPopulateCrpIdAndReturnOperatorDetailsMap() {
        // Arrange
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        when(taskParamService.getVehicleOperators(testTask))
                .thenReturn(Arrays.asList(testVehicleOperatorDTO));
        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(operatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList);

        // Assert
        assertNotNull(result);
        assertEquals(operatorDetailsMap, result);
        assertEquals("CRP001", testVehicleOperatorResource.getCrpId());

        verify(taskParamService).getVehicleOperators(testTask);
        verify(resourceServiceAdapter).getOperatorDetailsMap(any(GetOperatorListRequest.class));
    }

    @Test
    void populateCrpIdInTransportOrder_WithEmptyTaskList_ShouldReturnEmptyMap() {
        // Arrange
        List<Task> emptyTaskList = Collections.emptyList();

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, emptyTaskList);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verifyNoInteractions(taskParamService);
        verifyNoInteractions(resourceServiceAdapter);
    }

    @Test
    void populateCrpIdInTransportOrder_WithNullTaskList_ShouldReturnEmptyMap() {
        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, null);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verifyNoInteractions(taskParamService);
        verifyNoInteractions(resourceServiceAdapter);
    }

    @Test
    void populateCrpIdInTransportOrder_WithNullTransportOrder_ShouldReturnEmptyMap() {
        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(null, testTaskList);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verifyNoInteractions(taskParamService);
        verifyNoInteractions(resourceServiceAdapter);
    }

    @Test
    void populateCrpIdInTransportOrder_WithTasksHavingNoVehicleOperators_ShouldReturnEmptyMap() {
        // Arrange
        when(taskParamService.getVehicleOperators(testTask))
                .thenReturn(Collections.emptyList());

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(taskParamService).getVehicleOperators(testTask);
        verifyNoInteractions(resourceServiceAdapter);
    }

    @Test
    void populateCrpIdInTransportOrder_WithNullTrips_ShouldReturnOperatorDetailsMapWithoutPopulation() {
        // Arrange
        testTransportOrder.setTrips(null);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        when(taskParamService.getVehicleOperators(testTask))
                .thenReturn(Arrays.asList(testVehicleOperatorDTO));
        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(operatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList);

        // Assert
        assertNotNull(result);
        assertEquals(operatorDetailsMap, result);
        // CRP ID should remain null since trips are null
        assertNull(testVehicleOperatorResource.getCrpId());

        verify(taskParamService).getVehicleOperators(testTask);
        verify(resourceServiceAdapter).getOperatorDetailsMap(any(GetOperatorListRequest.class));
    }

    @Test
    void populateCrpIdInTransportOrder_WithNullVehicleOperatorResources_ShouldReturnOperatorDetailsMap() {
        // Arrange
        testTrip.setVehicleOperatorResources(null);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        when(taskParamService.getVehicleOperators(testTask))
                .thenReturn(Arrays.asList(testVehicleOperatorDTO));
        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(operatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList);

        // Assert
        assertNotNull(result);
        assertEquals(operatorDetailsMap, result);

        verify(taskParamService).getVehicleOperators(testTask);
        verify(resourceServiceAdapter).getOperatorDetailsMap(any(GetOperatorListRequest.class));
    }

    @Test
    void populateCrpIdInTransportOrder_WithNullTask_ShouldSkipNullTaskAndProcessOthers() {
        // Arrange
        Task validTask = new Task();
        validTask.setId(2L);
        validTask.setCode("TASK002");

        List<Task> taskListWithNull = Arrays.asList(null, validTask);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        when(taskParamService.getVehicleOperators(validTask))
                .thenReturn(Arrays.asList(testVehicleOperatorDTO));
        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(operatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, taskListWithNull);

        // Assert
        assertNotNull(result);
        assertEquals(operatorDetailsMap, result);

        verify(taskParamService, never()).getVehicleOperators(null);
        verify(taskParamService).getVehicleOperators(validTask);
        verify(resourceServiceAdapter).getOperatorDetailsMap(any(GetOperatorListRequest.class));
    }

    @Test
    void populateCrpIdInTransportOrder_WithVehicleOperatorHavingNullExternalResourceId_ShouldSkipPopulation() {
        // Arrange
        testVehicleOperatorResource.setExternalResourceId(null);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        when(taskParamService.getVehicleOperators(testTask))
                .thenReturn(Arrays.asList(testVehicleOperatorDTO));
        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(operatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList);

        // Assert
        assertNotNull(result);
        assertEquals(operatorDetailsMap, result);
        assertNull(testVehicleOperatorResource.getCrpId());

        verify(taskParamService).getVehicleOperators(testTask);
        verify(resourceServiceAdapter).getOperatorDetailsMap(any(GetOperatorListRequest.class));
    }

    @Test
    void populateCrpIdInTransportOrder_WithOperatorDetailsHavingNoCrpDetails_ShouldSetCrpIdToNull() {
        // Arrange
        GetOperatorDetailsListResponse operatorWithoutCrp = new GetOperatorDetailsListResponse();
        operatorWithoutCrp.setId(1L);
        operatorWithoutCrp.setCrpDetails(null);

        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", operatorWithoutCrp);

        when(taskParamService.getVehicleOperators(testTask))
                .thenReturn(Arrays.asList(testVehicleOperatorDTO));
        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(operatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList);

        // Assert
        assertNotNull(result);
        assertEquals(operatorDetailsMap, result);
        assertNull(testVehicleOperatorResource.getCrpId());

        verify(taskParamService).getVehicleOperators(testTask);
        verify(resourceServiceAdapter).getOperatorDetailsMap(any(GetOperatorListRequest.class));
    }

    @Test
    void populateCrpIdInTransportOrder_WithNonMatchingOperatorId_ShouldSetCrpIdToNull() {
        // Arrange
        testVehicleOperatorResource.setExternalResourceId("999"); // Non-matching ID
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        when(taskParamService.getVehicleOperators(testTask))
                .thenReturn(Arrays.asList(testVehicleOperatorDTO));
        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(operatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList);

        // Assert
        assertNotNull(result);
        assertEquals(operatorDetailsMap, result);
        assertNull(testVehicleOperatorResource.getCrpId());

        verify(taskParamService).getVehicleOperators(testTask);
        verify(resourceServiceAdapter).getOperatorDetailsMap(any(GetOperatorListRequest.class));
    }

    @Test
    void populateCrpIdInTransportOrder_WithMultipleTasksAndResources_ShouldProcessAll() {
        // Arrange
        Task task2 = new Task();
        task2.setId(2L);
        task2.setCode("TASK002");

        VehicleOperatorResource resource2 = new VehicleOperatorResource();
        resource2.setExternalResourceId("2");
        resource2.setCrpId(null);

        Trip trip2 = new Trip();
        trip2.setVehicleOperatorResources(Set.of(resource2));

        testTransportOrder.setTrips(Set.of(testTrip, trip2));

        ParamValueVehicleOperatorDTO vehicleOperator2 = new ParamValueVehicleOperatorDTO();
        vehicleOperator2.setExternalResourceId("2");

        GetOperatorDetailsListResponse operatorDetails2 = new GetOperatorDetailsListResponse();
        operatorDetails2.setId(2L);
        GetOperatorDetailsListResponse.CrpDetailsResponse crpDetails2 = new GetOperatorDetailsListResponse.CrpDetailsResponse();
        crpDetails2.setCrpUserUUID("CRP002");
        operatorDetails2.setCrpDetails(crpDetails2);

        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails, "OP002", operatorDetails2);

        when(taskParamService.getVehicleOperators(testTask))
                .thenReturn(Arrays.asList(testVehicleOperatorDTO));
        when(taskParamService.getVehicleOperators(task2))
                .thenReturn(Arrays.asList(vehicleOperator2));
        when(resourceServiceAdapter.getOperatorDetailsMap(any(GetOperatorListRequest.class)))
                .thenReturn(operatorDetailsMap);

        // Act
        Map<String, GetOperatorDetailsListResponse> result =
                vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, Arrays.asList(testTask, task2));

        // Assert
        assertNotNull(result);
        assertEquals(operatorDetailsMap, result);
        assertEquals("CRP001", testVehicleOperatorResource.getCrpId());
        assertEquals("CRP002", resource2.getCrpId());

        verify(taskParamService).getVehicleOperators(testTask);
        verify(taskParamService).getVehicleOperators(task2);
        verify(resourceServiceAdapter).getOperatorDetailsMap(any(GetOperatorListRequest.class));
    }
}