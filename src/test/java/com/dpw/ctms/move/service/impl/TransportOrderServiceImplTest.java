package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.integration.request.transportorder.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;
import com.dpw.ctms.move.integration.response.transportorder.TransportOrderResponse;
import com.dpw.ctms.move.mapper.TransportOrderMapper;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.IVehicleOperatorService;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.exception.TMSExceptionErrorCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TransportOrderServiceImplTest {

    @Mock
    private TransportOrderMapper transportOrderMapper;

    @Mock
    private IVehicleOperatorService vehicleOperatorService;

    @Mock
    private ITaskService taskService;

    @InjectMocks
    private TransportOrderServiceImpl transportOrderService;

    private TransportOrderFTLCreateRequest testCreateRequest;
    private TransportOrderDTO testTransportOrderDTO;
    private TransportOrder testTransportOrder;
    private TransportOrderResponse testTransportOrderResponse;
    private List<Task> testTaskList;
    private Task testTask;
    private Map<String, GetOperatorDetailsListResponse> testOperatorDetailsMap;
    private GetOperatorDetailsListResponse testOperatorDetails;

    @BeforeEach
    void setUp() {
        setupTestData();
    }

    private void setupTestData() {
        // Create test request
        testCreateRequest = new TransportOrderFTLCreateRequest();
        // Set required fields for the request

        // Create test DTO
        testTransportOrderDTO = new TransportOrderDTO();
        testTransportOrderDTO.setCode("TO001");

        // Create test entity
        testTransportOrder = new TransportOrder();
        testTransportOrder.setId(1L);
        testTransportOrder.setCode("TO001");

        // Create test response
        testTransportOrderResponse = new TransportOrderResponse();
        testTransportOrderResponse.setCode("TO001");

        // Create test task
        testTask = new Task();
        testTask.setId(1L);
        testTask.setCode("TASK001");
        testTaskList = Arrays.asList(testTask);

        // Create test operator details
        testOperatorDetails = new GetOperatorDetailsListResponse();
        testOperatorDetails.setId(1L);
        GetOperatorDetailsListResponse.CrpDetailsResponse crpDetails = new GetOperatorDetailsListResponse.CrpDetailsResponse();
        crpDetails.setCrpUserUUID("CRP001");
        testOperatorDetails.setCrpDetails(crpDetails);

        testOperatorDetailsMap = Map.of("OP001", testOperatorDetails);
    }

    @Test
    void createTransportOrderFTLFulfilment_WithValidRequest_ShouldProcessSuccessfully() {
        // Arrange
        when(transportOrderMapper.toDTO(testCreateRequest)).thenReturn(testTransportOrderDTO);
        when(transportOrderMapper.toEntity(testTransportOrderDTO)).thenReturn(testTransportOrder);
        when(vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList))
                .thenReturn(testOperatorDetailsMap);
        doNothing().when(taskService).registerTaskInstances(testTaskList, testOperatorDetailsMap);

        // Mock the private methods by using spy
        TransportOrderServiceImpl spyService = spy(transportOrderService);
        doNothing().when(spyService).processTransportOrder(testTransportOrderDTO, testTransportOrder);
        doReturn(testTaskList).when(spyService).extractTasks(testTransportOrder);
        doReturn(testTransportOrderResponse).when(spyService).buildResponse(testTransportOrder);

        // Act
        TransportOrderResponse result = spyService.createTransportOrderFTLFulfilment(testCreateRequest);

        // Assert
        assertNotNull(result);
        assertEquals(testTransportOrderResponse, result);

        verify(transportOrderMapper).toDTO(testCreateRequest);
        verify(transportOrderMapper).toEntity(testTransportOrderDTO);
        verify(vehicleOperatorService).populateCrpIdInTransportOrder(testTransportOrder, testTaskList);
        verify(taskService).registerTaskInstances(testTaskList, testOperatorDetailsMap);
    }

    @Test
    void createTransportOrderFTLFulfilment_WithEmptyTaskList_ShouldProcessWithoutTaskRegistration() {
        // Arrange
        List<Task> emptyTaskList = Collections.emptyList();
        Map<String, GetOperatorDetailsListResponse> emptyOperatorDetailsMap = Collections.emptyMap();

        when(transportOrderMapper.toDTO(testCreateRequest)).thenReturn(testTransportOrderDTO);
        when(transportOrderMapper.toEntity(testTransportOrderDTO)).thenReturn(testTransportOrder);
        when(vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, emptyTaskList))
                .thenReturn(emptyOperatorDetailsMap);
        doNothing().when(taskService).registerTaskInstances(emptyTaskList, emptyOperatorDetailsMap);

        // Mock the private methods
        TransportOrderServiceImpl spyService = spy(transportOrderService);
        doNothing().when(spyService).processTransportOrder(testTransportOrderDTO, testTransportOrder);
        doReturn(emptyTaskList).when(spyService).extractTasks(testTransportOrder);
        doReturn(testTransportOrderResponse).when(spyService).buildResponse(testTransportOrder);

        // Act
        TransportOrderResponse result = spyService.createTransportOrderFTLFulfilment(testCreateRequest);

        // Assert
        assertNotNull(result);
        assertEquals(testTransportOrderResponse, result);

        verify(vehicleOperatorService).populateCrpIdInTransportOrder(testTransportOrder, emptyTaskList);
        verify(taskService).registerTaskInstances(emptyTaskList, emptyOperatorDetailsMap);
    }

    @Test
    void createTransportOrderFTLFulfilment_WhenVehicleOperatorServiceThrowsException_ShouldPropagateException() {
        // Arrange
        RuntimeException serviceException = new RuntimeException("Service error");
        
        when(transportOrderMapper.toDTO(testCreateRequest)).thenReturn(testTransportOrderDTO);
        when(transportOrderMapper.toEntity(testTransportOrderDTO)).thenReturn(testTransportOrder);
        when(vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList))
                .thenThrow(serviceException);

        // Mock the private methods
        TransportOrderServiceImpl spyService = spy(transportOrderService);
        doNothing().when(spyService).processTransportOrder(testTransportOrderDTO, testTransportOrder);
        doReturn(testTaskList).when(spyService).extractTasks(testTransportOrder);

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                spyService.createTransportOrderFTLFulfilment(testCreateRequest));

        assertEquals("Service error", exception.getMessage());
        verify(vehicleOperatorService).populateCrpIdInTransportOrder(testTransportOrder, testTaskList);
        verifyNoInteractions(taskService);
    }

    @Test
    void createTransportOrderFTLFulfilment_WhenTaskServiceThrowsException_ShouldPropagateException() {
        // Arrange
        RuntimeException taskServiceException = new RuntimeException("Task service error");
        
        when(transportOrderMapper.toDTO(testCreateRequest)).thenReturn(testTransportOrderDTO);
        when(transportOrderMapper.toEntity(testTransportOrderDTO)).thenReturn(testTransportOrder);
        when(vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList))
                .thenReturn(testOperatorDetailsMap);
        doThrow(taskServiceException).when(taskService).registerTaskInstances(testTaskList, testOperatorDetailsMap);

        // Mock the private methods
        TransportOrderServiceImpl spyService = spy(transportOrderService);
        doNothing().when(spyService).processTransportOrder(testTransportOrderDTO, testTransportOrder);
        doReturn(testTaskList).when(spyService).extractTasks(testTransportOrder);

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                spyService.createTransportOrderFTLFulfilment(testCreateRequest));

        assertEquals("Task service error", exception.getMessage());
        verify(vehicleOperatorService).populateCrpIdInTransportOrder(testTransportOrder, testTaskList);
        verify(taskService).registerTaskInstances(testTaskList, testOperatorDetailsMap);
    }

    @Test
    void populateCrpIdInTransportOrder_WithNullTransportOrder_ShouldReturnEmptyMap() {
        // Arrange
        TransportOrderServiceImpl spyService = spy(transportOrderService);

        // Act
        Map<String, GetOperatorDetailsListResponse> result = 
                spyService.populateCrpIdInTransportOrder(null, testTaskList);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verifyNoInteractions(vehicleOperatorService);
    }

    @Test
    void populateCrpIdInTransportOrder_WithEmptyTaskList_ShouldReturnEmptyMap() {
        // Arrange
        TransportOrderServiceImpl spyService = spy(transportOrderService);
        List<Task> emptyTaskList = Collections.emptyList();

        // Act
        Map<String, GetOperatorDetailsListResponse> result = 
                spyService.populateCrpIdInTransportOrder(testTransportOrder, emptyTaskList);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verifyNoInteractions(vehicleOperatorService);
    }

    @Test
    void populateCrpIdInTransportOrder_WithValidData_ShouldReturnOperatorDetailsMap() {
        // Arrange
        when(vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList))
                .thenReturn(testOperatorDetailsMap);

        TransportOrderServiceImpl spyService = spy(transportOrderService);

        // Act
        Map<String, GetOperatorDetailsListResponse> result = 
                spyService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList);

        // Assert
        assertNotNull(result);
        assertEquals(testOperatorDetailsMap, result);
        verify(vehicleOperatorService).populateCrpIdInTransportOrder(testTransportOrder, testTaskList);
    }

    @Test
    void populateCrpIdInTransportOrder_WhenVehicleOperatorServiceThrowsException_ShouldThrowTMSException() {
        // Arrange
        RuntimeException serviceException = new RuntimeException("Service error");
        when(vehicleOperatorService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList))
                .thenThrow(serviceException);

        TransportOrderServiceImpl spyService = spy(transportOrderService);

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () ->
                spyService.populateCrpIdInTransportOrder(testTransportOrder, testTaskList));

        assertEquals(TMSExceptionErrorCode.INTERNAL_ERROR.name(), exception.getErrorCode());
        assertTrue(exception.getMessage().contains("Failed to populate crpId in vehicle operator resources"));
        assertNotNull(exception.getMetadata());
        assertEquals("TO001", exception.getMetadata().get("transportOrderCode"));
        assertEquals("Service error", exception.getMetadata().get("errorDetails"));

        verify(vehicleOperatorService).populateCrpIdInTransportOrder(testTransportOrder, testTaskList);
    }
}
