package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse.CrpDetailsResponse;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.response.TransportOrderResponse;
import com.dpw.ctms.move.mapper.TransportOrderMapper;
import com.dpw.ctms.move.mapper.IEntityRelationshipMapper;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.IVehicleOperatorService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.TransportOrderFilteringService;
import com.dpw.tmsutils.exception.TMSException;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INTERNAL_ERROR;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TransportOrderServiceImplTest {

    @Mock
    private TransportOrderMapper transportOrderMapper;

    @Mock
    private TransportOrderRepository transportOrderRepository;

    @Mock
    private List<IEntityRelationshipMapper> relationshipMappers;

    @Mock
    private TransportOrderFilteringService transportOrderFilteringService;

    @Mock
    private ITaskService taskService;

    @Mock
    private ITripService tripService;

    @Mock
    private IShipmentService shipmentService;

    @Mock
    private IVehicleOperatorService vehicleOperatorService;

    @InjectMocks
    private TransportOrderServiceImpl transportOrderService;

    private TransportOrderFTLCreateRequest testCreateRequest;
    private TransportOrderDTO testTransportOrderDTO;
    private TransportOrder testTransportOrder;
    private TransportOrderResponse testTransportOrderResponse;
    private List<Task> testTaskList;
    private Task testTask;
    private Map<String, GetOperatorDetailsListResponse> testOperatorDetailsMap;
    private GetOperatorDetailsListResponse testOperatorDetails;

    @BeforeEach
    void setUp() {
        setupTestData();
    }

    private void setupTestData() {
        // Create test request
        testCreateRequest = new TransportOrderFTLCreateRequest();
        // Set required fields for the request

        // Create test DTO
        testTransportOrderDTO = new TransportOrderDTO();
        testTransportOrderDTO.setCode("TO001");

        // Create test entity with trips, stops, and tasks
        testTransportOrder = new TransportOrder();
        testTransportOrder.setId(1L);
        testTransportOrder.setCode("TO001");

        // Create test task
        testTask = new Task();
        testTask.setId(1L);
        testTask.setCode("TASK001");

        // Create stop task
        StopTask stopTask = new StopTask();
        stopTask.setTask(testTask);

        // Create stop
        Stop stop = new Stop();
        stop.setId(1L);
        stop.setStopTasks(List.of(stopTask));

        // Create trip
        Trip trip = new Trip();
        trip.setId(1L);
        trip.setCode("TRIP001");
        trip.setStops(Set.of(stop));

        // Set trips in transport order
        testTransportOrder.setTrips(Set.of(trip));

        testTaskList = List.of(testTask);

        // Create test response
        testTransportOrderResponse = TransportOrderResponse.builder()
                .transportOrderCode("TO001")
                .isSuccess(true)
                .message("Success")
                .build();



        // Create test operator details
        testOperatorDetails = new GetOperatorDetailsListResponse();
        testOperatorDetails.setId(1L);
        testOperatorDetails.setCode("OP001");

        CrpDetailsResponse crpDetails = new CrpDetailsResponse();
        crpDetails.setCrpUserUUID("crp-uuid-123");
        testOperatorDetails.setCrpDetails(crpDetails);

        testOperatorDetailsMap = Map.of("OP001", testOperatorDetails);
    }

    @Test
    void createTransportOrderFTLFulfilment_WithValidRequest_ShouldProcessSuccessfully() {
        // Arrange
        when(transportOrderMapper.toDTO(testCreateRequest)).thenReturn(testTransportOrderDTO);
        when(transportOrderMapper.toEntity(testTransportOrderDTO)).thenReturn(testTransportOrder);
        when(vehicleOperatorService.populateCrpIdInTransportOrder(any(TransportOrder.class), any(List.class)))
                .thenReturn(testOperatorDetailsMap);
        doNothing().when(taskService).registerTaskInstances(any(List.class), any(Map.class));
        when(transportOrderRepository.save(any(TransportOrder.class))).thenReturn(testTransportOrder);

        // Act
        TransportOrderResponse result = transportOrderService.createTransportOrderFTLFulfilment(testCreateRequest);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getTransportOrderCode());
        assertTrue(result.getIsSuccess());

        verify(transportOrderMapper).toDTO(testCreateRequest);
        verify(transportOrderMapper).toEntity(testTransportOrderDTO);
        verify(vehicleOperatorService).populateCrpIdInTransportOrder(any(TransportOrder.class), any(List.class));
        verify(taskService).registerTaskInstances(any(List.class), any(Map.class));
    }

    @Test
    void createTransportOrderFTLFulfilment_WithEmptyTaskList_ShouldProcessWithoutTaskRegistration() {
        // Arrange
        Map<String, GetOperatorDetailsListResponse> emptyOperatorDetailsMap = Collections.emptyMap();

        when(transportOrderMapper.toDTO(testCreateRequest)).thenReturn(testTransportOrderDTO);
        when(transportOrderMapper.toEntity(testTransportOrderDTO)).thenReturn(testTransportOrder);
        when(vehicleOperatorService.populateCrpIdInTransportOrder(any(TransportOrder.class), any(List.class)))
                .thenReturn(emptyOperatorDetailsMap);
        doNothing().when(taskService).registerTaskInstances(any(List.class), any(Map.class));
        when(transportOrderRepository.save(any(TransportOrder.class))).thenReturn(testTransportOrder);

        // Act
        TransportOrderResponse result = transportOrderService.createTransportOrderFTLFulfilment(testCreateRequest);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getTransportOrderCode());
        assertTrue(result.getIsSuccess());

        verify(vehicleOperatorService).populateCrpIdInTransportOrder(any(TransportOrder.class), any(List.class));
        verify(taskService).registerTaskInstances(any(List.class), any(Map.class));
    }

    @Test
    void createTransportOrderFTLFulfilment_WhenVehicleOperatorServiceThrowsException_ShouldPropagateException() {
        // Arrange
        RuntimeException serviceException = new RuntimeException("Service error");

        when(transportOrderMapper.toDTO(testCreateRequest)).thenReturn(testTransportOrderDTO);
        when(transportOrderMapper.toEntity(testTransportOrderDTO)).thenReturn(testTransportOrder);
        when(vehicleOperatorService.populateCrpIdInTransportOrder(any(TransportOrder.class), any(List.class)))
                .thenThrow(serviceException);

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () ->
                transportOrderService.createTransportOrderFTLFulfilment(testCreateRequest));

        assertEquals(INTERNAL_ERROR.name(), exception.getErrorCode());
        verify(vehicleOperatorService).populateCrpIdInTransportOrder(any(TransportOrder.class), any(List.class));
        verifyNoInteractions(taskService);
    }

    @Test
    void createTransportOrderFTLFulfilment_WhenTaskServiceThrowsException_ShouldPropagateException() {
        // Arrange
        RuntimeException taskServiceException = new RuntimeException("Task service error");

        when(transportOrderMapper.toDTO(testCreateRequest)).thenReturn(testTransportOrderDTO);
        when(transportOrderMapper.toEntity(testTransportOrderDTO)).thenReturn(testTransportOrder);
        when(vehicleOperatorService.populateCrpIdInTransportOrder(any(TransportOrder.class), any(List.class)))
                .thenReturn(testOperatorDetailsMap);
        doThrow(taskServiceException).when(taskService).registerTaskInstances(any(List.class), any(Map.class));

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () ->
                transportOrderService.createTransportOrderFTLFulfilment(testCreateRequest));

        assertEquals(INTERNAL_ERROR.name(), exception.getErrorCode());
        verify(vehicleOperatorService).populateCrpIdInTransportOrder(any(TransportOrder.class), any(List.class));
        verify(taskService).registerTaskInstances(any(List.class), any(Map.class));
    }

    // Note: Tests for private methods populateCrpIdInTransportOrder and registerTaskInstances
    // are covered through the public createTransportOrderFTLFulfilment method tests above
}
