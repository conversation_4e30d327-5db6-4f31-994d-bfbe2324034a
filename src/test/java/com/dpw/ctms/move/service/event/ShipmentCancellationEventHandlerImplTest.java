package com.dpw.ctms.move.service.event;

import com.dpw.ctms.move.dto.producer.ShipmentCancellationEventRequestDTO;
import com.dpw.ctms.move.request.message.ShipmentCancellationAckMessage;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.IEventProcessorService;

import com.dpw.ctms.move.service.event.impl.ShipmentCancellationEventHandlerImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ShipmentCancellationEventHandlerImplTest {

    private IEventProcessorService<ShipmentCancellationAckMessage> eventProcessorService;
    private IEntityCancellationEventManager entityCancellationEventManager;
    private ShipmentCancellationEventHandlerImpl handler;

    @BeforeEach
    void setUp() {
        eventProcessorService = mock(IEventProcessorService.class);
        entityCancellationEventManager = mock(IEntityCancellationEventManager.class);
        handler = new ShipmentCancellationEventHandlerImpl(eventProcessorService, entityCancellationEventManager);
        ReflectionTestUtils.setField(handler, "shipmentCancellationAckTopicName", "test-topic");
    }

    @Test
    void shouldRegisterHandlerOnPostConstruct() {
        handler.register();
        verify(entityCancellationEventManager).register(eq("SHIPMENT"), eq(handler));
    }

    @Test
    void shouldProcessEventSuccessfully() {
        // given
        ShipmentCancellationEventRequestDTO dto = new ShipmentCancellationEventRequestDTO();
        dto.setShipmentCodes(List.of("SHIP123"));
        dto.setIsCancelled(true);
        dto.setUpdatedBy("gaurav");
        dto.setUpdatedAt(System.currentTimeMillis());
        dto.setComments("Cancelled due to delay");

        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(any(), any())).thenReturn(mockResponse);

        // when
        IntegratorMessageResponse response = handler.acknowledgeEntityCancellationEvent(dto);

        // then
        assertNotNull(response);
        assertTrue(response.isSuccess());
        verify(eventProcessorService, times(1)).processRequest(eq("SHIPMENT_CANCELLATION_CONFIRMATION_EVENT"), any());
    }

    @Test
    void shouldHandleExceptionAndReturnEmptyResponse() {
        // given
        ShipmentCancellationEventRequestDTO dto = new ShipmentCancellationEventRequestDTO();
        dto.setShipmentCodes(List.of("SHIP456"));
        dto.setIsCancelled(false);
        dto.setUpdatedBy("system");
        dto.setUpdatedAt(System.currentTimeMillis());
        dto.setComments("Testing failure case");

        when(eventProcessorService.processRequest(any(), any())).thenThrow(new RuntimeException("Kafka failure"));

        // when
        IntegratorMessageResponse response = handler.acknowledgeEntityCancellationEvent(dto);

        // then
        assertNotNull(response);
        assertNull(response.getMessage()); // or check for empty/false response
        verify(eventProcessorService).processRequest(any(), any());
    }
}

