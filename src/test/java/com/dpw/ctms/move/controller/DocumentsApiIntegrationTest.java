package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.*;
import com.dpw.ctms.move.repository.DocumentRepository;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.response.DocumentDownloadResponse;
import com.dpw.ctms.move.response.PreSignedUrlResponse;
import com.dpw.ctms.move.service.document.DocumentGeneratorFactory;
import com.dpw.ctms.move.specification.DocumentSpecifications;
import com.dpw.ctms.move.testcontainers.TestDatabaseManager;
import com.dpw.ctms.move.util.CanonicalChecksum;
import com.dpw.tmsutils.schemaobjects.*;
import com.dpw.tmsutils.service.DocumentService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import com.dpw.ctms.move.response.DocumentErrorResponse;

import java.util.*;

import static com.dpw.tmsutils.constant.RequestConstants.TENANT_HEADER;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class DocumentsApiIntegrationTest extends IntegrationTestBase {

    @Autowired
    private TripRepository tripRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @MockBean
    private DocumentService documentService;

    @MockBean
    private DocumentGeneratorFactory documentGeneratorFactory;

    @Autowired
    private CanonicalChecksum canonicalChecksum;

    private String tripCode;
    private Trip trip;
    private Object testJsonObject;
    private String testChecksum;

    @BeforeEach
    void setUp() {
        
        // Create a test trip
        tripCode = "TRIP_" + UUID.randomUUID().toString().substring(0, 8);
        trip = new Trip();
        trip.setCode(tripCode);
        trip.setStatus(TripStatus.CREATED);
        trip = tripRepository.save(trip);
        
        // Create test JSON object and checksum
        testJsonObject = Map.of("tripCode", tripCode, "test", "data");
        testChecksum = canonicalChecksum.generateChecksum(testJsonObject);
    }

    @AfterEach
    void tearDown() {
        documentRepository.deleteAll();
        tripRepository.deleteAll();
        TestDatabaseManager.cleanupCfrSchema();
    }

    @Test
    void shouldGenerateNewBolDocumentWhenNoExistingDocument() throws Exception {
        // Given
        String fileIdentifier = UUID.randomUUID().toString();
        String presignedUrl = "https://example.com/presigned-url";

        mockDocumentGeneratorFactory(testJsonObject);
        mockDocumentService(fileIdentifier, presignedUrl);

        // When
        MvcResult result = mockMvc.perform(post("/v1/documents/trip/{tripCode}/download/bol", tripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        // Then
        DocumentDownloadResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(),
                new TypeReference<DocumentDownloadResponse>() {}
        );

        assertNotNull(response);
        assertEquals(presignedUrl, response.getPresignedDownloadUrl());

        // Verify document was saved to database
        Optional<Document> savedDocument = documentRepository.findOne(
                DocumentSpecifications.bolDocumentSpec(tripCode, EntityType.TRIP.name(), DocumentStatus.ACTIVE, DocumentOperationType.DOWNLOAD, testChecksum, DocumentType.BOL));
        assertTrue(savedDocument.isPresent());
        assertEquals(fileIdentifier, savedDocument.get().getFileIdentifier());
        assertEquals(tripCode, savedDocument.get().getEntityId());
        assertEquals(DocumentType.BOL, savedDocument.get().getDocumentType());

        // Verify service calls
        verify(documentService).getBol(any(PrintBolRequest.class), anyString());
        verifyNoMoreInteractions(documentService);
    }

    @Test
    void shouldGetPresignedUrlForExistingDocument() throws Exception {
        // Given
        String existingFileIdentifier = UUID.randomUUID().toString();
        String newPresignedUrl = "https://example.com/new-presigned-url";

        // Save existing document
        Document existingDocument = Document.builder()
                .entityId(tripCode)
                .entityType(EntityType.TRIP.name())
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.DOWNLOAD)
                .documentType(DocumentType.BOL)
                .checksum(testChecksum)
                .fileIdentifier(existingFileIdentifier)
                .build();
        documentRepository.save(existingDocument);

        mockDocumentGeneratorFactory(testJsonObject);
        mockGetPresignedUrlService(existingFileIdentifier, newPresignedUrl);

        // When
        MvcResult result = mockMvc.perform(post("/v1/documents/trip/{tripCode}/download/bol", tripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        // Then
        DocumentDownloadResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(),
                new TypeReference<DocumentDownloadResponse>() {}
        );

        assertNotNull(response);
        assertEquals(newPresignedUrl, response.getPresignedDownloadUrl());

        // Verify getBol was not called for existing document
        verify(documentService, never()).getBol(any(PrintBolRequest.class), anyString());
        // Verify getDownloadPreSignedURL was called
        verify(documentService).getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class));
    }

    @Test
    void shouldHandleDocumentServiceError() throws Exception {
        // Given
        mockDocumentGeneratorFactory(testJsonObject);
        when(documentService.getBol(any(PrintBolRequest.class), anyString()))
                .thenThrow(new RuntimeException("Document service error"));

        // When & Then
        mockMvc.perform(post("/v1/documents/trip/{tripCode}/download/bol", tripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError());
    }

    @Test
    void shouldHandleInvalidTripCode() throws Exception {
        // Given
        String invalidTripCode = "INVALID_TRIP";
        mockDocumentGeneratorFactory(testJsonObject);

        // When & Then
        mockMvc.perform(post("/v1/documents/trip/{tripCode}/download/bol", invalidTripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError());
    }

    @Test
    void shouldHandleMissingTenantHeader() throws Exception {
        // When & Then
        mockMvc.perform(post("/v1/documents/trip/{tripCode}/download/bol", tripCode)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is4xxClientError());
    }

    @Test
    void shouldHandlePresignedUrlServiceError() throws Exception {
        // Given
        String existingFileIdentifier = UUID.randomUUID().toString();

        // Save existing document
        Document existingDocument = Document.builder()
                .entityId(tripCode)
                .entityType(EntityType.TRIP.name())
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.DOWNLOAD)
                .documentType(DocumentType.BOL)
                .checksum(testChecksum)
                .fileIdentifier(existingFileIdentifier)
                .build();
        documentRepository.save(existingDocument);

        mockDocumentGeneratorFactory(testJsonObject);
        when(documentService.getDownloadPreSignedURL(any(GetDownloadPreSignedURLRequest.class)))
                .thenThrow(new RuntimeException("Presigned URL service error"));

        // When & Then
        mockMvc.perform(post("/v1/documents/trip/{tripCode}/download/bol", tripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError());
    }

    @Test
    void shouldHandleEmptyFileIdentifierList() throws Exception {
        // Given
        String existingFileIdentifier = UUID.randomUUID().toString();

        // Save existing document
        Document existingDocument = Document.builder()
                .entityId(tripCode)
                .entityType(EntityType.TRIP.name())
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.DOWNLOAD)
                .documentType(DocumentType.BOL)
                .checksum(testChecksum)
                .fileIdentifier(existingFileIdentifier)
                .build();
        documentRepository.save(existingDocument);

        mockDocumentGeneratorFactory(testJsonObject);

        // Mock empty response
        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> emptyResponse = 
                new DocumentServiceResponse<>();
        emptyResponse.setData(Collections.emptyList());
        when(documentService.getDownloadPreSignedURL(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(emptyResponse);

        // When & Then
        mockMvc.perform(post("/v1/documents/trip/{tripCode}/download/bol", tripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError());
    }

    private void mockDocumentGeneratorFactory(Object jsonObject) {
        var mockGenerator = mock(com.dpw.ctms.move.service.document.DocumentGenerator.class);
        when(mockGenerator.generateJson(eq(tripCode), eq(Tenant.CFR))).thenReturn(jsonObject);
        when(documentGeneratorFactory.getGenerator(DocumentType.BOL)).thenReturn(mockGenerator);
    }

    private void mockDocumentService(String fileIdentifier, String presignedUrl) {
        PrintBolResponse printBolResponse = new PrintBolResponse();
        printBolResponse.setFileIdentifier(fileIdentifier);
        printBolResponse.setPresignedDownloadUrl(presignedUrl);

        DocumentServiceResponse<PrintBolResponse> response = new DocumentServiceResponse<>();
        response.setData(printBolResponse);

        when(documentService.getBol(any(PrintBolRequest.class), anyString()))
                .thenReturn(response);
    }

    private void mockGetPresignedUrlService(String fileIdentifier, String presignedUrl) {
        DownloadPreSignedURLResponse downloadResponse = DownloadPreSignedURLResponse.builder()
                .fileIdentifier(fileIdentifier)
                .preSignedUrl(presignedUrl)
                .build();

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> response = 
                new DocumentServiceResponse<>();
        response.setData(Collections.singletonList(downloadResponse));

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(response);
    }

    @Test
    void getPreSignedUrl_shouldReturnSuccessResponse() throws Exception {
        // Given
        String expectedPreSignedUrl = "https://example.com/presigned-url";

        GetPreSignedURLResponse preSignedUrlResponse = GetPreSignedURLResponse.builder()
                .preSignedUrl(expectedPreSignedUrl)
                .build();



        DocumentServiceResponse<GetPreSignedURLResponse> serviceResponse = DocumentServiceResponse.<GetPreSignedURLResponse>builder()
                .data(preSignedUrlResponse)
                .error(false)
                .build();

        when(documentService.getPreSignedURL(any(GetPreSignedURLRequest.class)))
                .thenReturn(serviceResponse);

        // When & Then
        MvcResult result = mockMvc.perform(get("/v1/documents/presigned-url")
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.preSignedURL").value(expectedPreSignedUrl))
                .andExpect(jsonPath("$.clientIdentifier").exists())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        PreSignedUrlResponse response = objectMapper.readValue(responseContent, new TypeReference<PreSignedUrlResponse>() {});

        assertNotNull(response);
        assertEquals(expectedPreSignedUrl, response.getPreSignedURL());
        assertNotNull(response.getClientIdentifier());
    }

    @Test
    void getPreSignedUrl_shouldReturnError_whenDocumentServiceFails() throws Exception {
        // Given
        String errorMessage = "Document service unavailable";

        DocumentServiceResponse<GetPreSignedURLResponse> serviceResponse = DocumentServiceResponse.<GetPreSignedURLResponse>builder()
                .error(true)
                .errorDescription(errorMessage)
                .build();

        when(documentService.getPreSignedURL(any(GetPreSignedURLRequest.class)))
                .thenReturn(serviceResponse);

        // When & Then
        mockMvc.perform(get("/v1/documents/presigned-url")
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().isUnprocessableEntity());
    }

    @Test
    void getDocumentErrors_shouldReturnErrorsForMissingFileIdentifiers() throws Exception {
        // Given
        String validFileId = "valid-file-" + UUID.randomUUID();
        String invalidFileId1 = "invalid-file-1";
        String invalidFileId2 = "invalid-file-2";
        
        // Create one valid document
        Document validDoc = Document.builder()
                .asyncMappingUUID(validFileId)
                .entityId(tripCode)
                .entityType(EntityType.TRIP.name())
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .documentType(DocumentType.BOL)
                .checksum(testChecksum)
                .build();
        documentRepository.save(validDoc);

        List<String> fileIdentifiers = Arrays.asList(validFileId, invalidFileId1, invalidFileId2);
        Map<String, Object> requestWrapper = Map.of("fileIdentifiers", fileIdentifiers);
        String requestBody = objectMapper.writeValueAsString(requestWrapper);

        // When & Then
        MvcResult result = mockMvc.perform(post("/v1/documents/errors")
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        // Then
        DocumentErrorResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(),
                new TypeReference<DocumentErrorResponse>() {}
        );

        assertNotNull(response);
        assertNotNull(response.getFailedDocumentDetails());
        assertEquals(2, response.getFailedDocumentDetails().size());
        
        List<String> errorFileIds = response.getFailedDocumentDetails().stream()
                .map(DocumentErrorResponse.FileErrorDetails::getFileIdentifier)
                .toList();
        
        assertTrue(errorFileIds.contains(invalidFileId1));
        assertTrue(errorFileIds.contains(invalidFileId2));
        assertFalse(errorFileIds.contains(validFileId));
    }

    @Test
    void getDocumentErrors_shouldReturnEmptyErrorsForAllValidFileIdentifiers() throws Exception {
        // Given
        String validFileId1 = "valid-file-1-" + UUID.randomUUID();
        String validFileId2 = "valid-file-2-" + UUID.randomUUID();
        
        // Create valid documents
        Document doc1 = Document.builder()
                .fileIdentifier(validFileId1)
                .entityId(tripCode)
                .entityType(EntityType.TRIP.name())
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .documentType(DocumentType.BOL)
                .checksum(testChecksum)
                .build();
        Document doc2 = Document.builder()
                .fileIdentifier(validFileId2)
                .entityId(tripCode)
                .entityType(EntityType.TRIP.name())
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .documentType(DocumentType.BOL)
                .checksum(testChecksum)
                .build();
        documentRepository.saveAll(Arrays.asList(doc1, doc2));

        List<String> fileIdentifiers = Arrays.asList(validFileId1, validFileId2);
        Map<String, Object> requestWrapper = Map.of("fileIdentifiers", fileIdentifiers);
        String requestBody = objectMapper.writeValueAsString(requestWrapper);

        // When & Then
        MvcResult result = mockMvc.perform(post("/v1/documents/errors")
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        // Then
        DocumentErrorResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(),
                new TypeReference<DocumentErrorResponse>() {}
        );

        assertNotNull(response);
        // When all documents are active, failedDocumentDetails is set to null
        assertNull(response.getFailedDocumentDetails());
    }

    @Test
    void getDocumentErrors_shouldReturn400ForEmptyList() throws Exception {
        // Given
        List<String> emptyList = Collections.emptyList();
        Map<String, Object> requestWrapper = Map.of("fileIdentifiers", emptyList);
        String requestBody = objectMapper.writeValueAsString(requestWrapper);

        // When & Then
        mockMvc.perform(post("/v1/documents/errors")
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getDocumentsByEntity_shouldReturnDocumentsForValidEntities() throws Exception {
        // Given
        String entityCode1 = "TASK-1234";
        String entityCode2 = "TASK-5678";
        String fileId1 = UUID.randomUUID().toString();
        String fileId2 = UUID.randomUUID().toString();

        // Create test documents
        Document doc1 = Document.builder()
                .entityId(entityCode1)
                .entityType("TASK")
                .asyncMappingUUID("uuid1")
                .fileName("doc1.pdf")
                .fileSize(1024)
                .fileType("application/pdf")
                .fileIdentifier(fileId1)
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        Document doc2 = Document.builder()
                .entityId(entityCode2)
                .entityType("TASK")
                .asyncMappingUUID("uuid2")
                .fileName("doc2.pdf")
                .fileSize(2048)
                .fileType("application/pdf")
                .fileIdentifier(fileId2)
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        documentRepository.saveAll(Arrays.asList(doc1, doc2));

        // Create request
        List<Map<String, String>> entityRequests = Arrays.asList(
                Map.of("entityCode", entityCode1, "entityType", "TASK"),
                Map.of("entityCode", entityCode2, "entityType", "TASK")
        );
        Map<String, Object> requestWrapper = Map.of("entityRequests", entityRequests);
        String requestBody = objectMapper.writeValueAsString(requestWrapper);

        // When & Then
        MvcResult result = mockMvc.perform(post("/v1/documents/list")
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        // Then
        String responseBody = result.getResponse().getContentAsString();
        assertNotNull(responseBody);
        assertFalse(responseBody.isEmpty());
        
        // Verify response structure contains expected data
        assertTrue(responseBody.contains("\"data\""));
        assertTrue(responseBody.contains("\"fileDetails\""));
        assertTrue(responseBody.contains("doc1.pdf"));
        assertTrue(responseBody.contains("doc2.pdf"));
        assertTrue(responseBody.contains(fileId1));
        assertTrue(responseBody.contains(fileId2));
    }

    @Test
    void getDocumentsByEntity_shouldReturnEmptyForNonExistentEntities() throws Exception {
        // Given
        List<Map<String, String>> entityRequests = Collections.singletonList(
                Map.of("entityCode", "TASK-9999", "entityType", "TASK")
        );
        Map<String, Object> requestWrapper = Map.of("entityRequests", entityRequests);
        String requestBody = objectMapper.writeValueAsString(requestWrapper);

        // When & Then
        MvcResult result = mockMvc.perform(post("/v1/documents/list")
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        // Then
        String responseBody = result.getResponse().getContentAsString();
        assertTrue(responseBody.contains("\"fileDetails\":[]"));
    }

    @Test
    void getDocumentsByEntity_shouldReturn200ForEmptyEntityRequests() throws Exception {
        // Given - empty entityRequests array is valid and should return empty results
        String requestBody = "{\"entityRequests\": []}";

        // When & Then
        MvcResult result = mockMvc.perform(post("/v1/documents/list")
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        // Then
        String responseBody = result.getResponse().getContentAsString();
        assertTrue(responseBody.contains("\"fileDetails\":[]"));
    }

    @Test
    void getFileDownloadUrls_shouldReturnPresignedUrlsForValidIdentifiers() throws Exception {
        // Given
        String fileId1 = "550e8400-e29b-41d4-a716-************";
        String fileId2 = "550e8400-e29b-41d4-a716-446655440002";
        String presignedUrl1 = "https://example.com/download1";
        String presignedUrl2 = "https://example.com/download2";

        // Mock document service response
        List<DownloadPreSignedURLResponse> downloadResponses = Arrays.asList(
                DownloadPreSignedURLResponse.builder()
                        .fileIdentifier(fileId1)
                        .preSignedUrl(presignedUrl1)
                        .build(),
                DownloadPreSignedURLResponse.builder()
                        .fileIdentifier(fileId2)
                        .preSignedUrl(presignedUrl2)
                        .build()
        );

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> serviceResponse = 
                new DocumentServiceResponse<>();
        serviceResponse.setData(downloadResponses);
        serviceResponse.setError(false);

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(serviceResponse);

        // When & Then
        MvcResult result = mockMvc.perform(get("/v1/documents/download-urls")
                        .header(TENANT_HEADER, "CFR")
                        .param("externalDocumentIdentifiers", fileId1, fileId2)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        // Then
        String responseBody = result.getResponse().getContentAsString();
        assertTrue(responseBody.contains(fileId1));
        assertTrue(responseBody.contains(fileId2));
        assertTrue(responseBody.contains(presignedUrl1));
        assertTrue(responseBody.contains(presignedUrl2));
    }

    @Test
    void getFileDownloadUrls_shouldReturn422ForInvalidUUIDFormat() throws Exception {
        // Given
        String invalidFileId = "invalid-uuid-format";

        // When & Then
        mockMvc.perform(get("/v1/documents/download-urls")
                        .header(TENANT_HEADER, "CFR")
                        .param("externalDocumentIdentifiers", invalidFileId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnprocessableEntity());
    }

    @Test
    void getFileDownloadUrls_shouldReturn422WhenDocumentServiceFails() throws Exception {
        // Given
        String validFileId = "550e8400-e29b-41d4-a716-************";

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> errorResponse = 
                new DocumentServiceResponse<>();
        errorResponse.setError(true);
        errorResponse.setErrorDescription("Document service unavailable");

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(errorResponse);

        // When & Then
        mockMvc.perform(get("/v1/documents/download-urls")
                        .header(TENANT_HEADER, "CFR")
                        .param("externalDocumentIdentifiers", validFileId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnprocessableEntity());
    }

    @Test
    void getFileDownloadUrls_shouldThrowErrorForEmptyParameterList() throws Exception {
        // Given - empty list
        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> serviceResponse = 
                new DocumentServiceResponse<>();
        serviceResponse.setData(Collections.emptyList());
        serviceResponse.setError(false);

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(serviceResponse);

        // When & Then
        mockMvc.perform(get("/v1/documents/download-urls")
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }
}