package com.dpw.ctms.move.integrations.adapter;

import com.dpw.ctms.move.integration.adapter.TaskServiceAdapter;
import com.dpw.ctms.move.integration.feignClient.TaskServiceClient;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.integration.response.TaskListResponse;
import com.dpw.tmsutils.exception.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskServiceAdapterTest {
    @Mock
    private TaskServiceClient taskServiceClient;

    @InjectMocks
    private TaskServiceAdapter taskServiceAdapter;

    @Test
    void registerTaskInstance_success() {
        TaskInstanceRegistrationRequest request = Mockito.mock(TaskInstanceRegistrationRequest.class);
        TaskInstanceRegistrationResponse responseObj = new TaskInstanceRegistrationResponse();
        TaskListResponse<TaskInstanceRegistrationResponse> listResponse = new TaskListResponse<>();
        listResponse.setList(Collections.singletonList(responseObj));
        when(taskServiceClient.registerTaskInstance(any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(ResponseEntity.ok(listResponse));

        List<TaskInstanceRegistrationResponse> result = taskServiceAdapter.registerTaskInstance(eq(request),
                Mockito.any(), Mockito.any(), Mockito.any());
        assertEquals(1, result.size());
        assertSame(responseObj, result.get(0));
    }

    @Test
    void registerTaskInstance_notFound() {
        TaskInstanceRegistrationRequest request = Mockito.mock(TaskInstanceRegistrationRequest.class);
        TMSFeignException ex = Mockito.mock(TMSFeignException.class);
        when(ex.getStatus()).thenReturn(HttpStatus.NOT_FOUND.value());
        when(taskServiceClient.registerTaskInstance(any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(ex);
        assertThrows(TMSException.class, () -> taskServiceAdapter.registerTaskInstance(eq(request), Mockito.any(), Mockito.any(), Mockito.any()));
    }

    @Test
    void registerTaskInstance_badRequest() {
        TaskInstanceRegistrationRequest request = Mockito.mock(TaskInstanceRegistrationRequest.class);
        TMSFeignException ex = Mockito.mock(TMSFeignException.class);
        when(ex.getStatus()).thenReturn(HttpStatus.BAD_REQUEST.value());
        when(taskServiceClient.registerTaskInstance(any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(ex);
        assertThrows(TMSException.class, () -> taskServiceAdapter.registerTaskInstance(eq(request), Mockito.any(),
                Mockito.any(), Mockito.any()));
    }

    @Test
    void registerTaskInstance_forbidden() {
        TaskInstanceRegistrationRequest request = Mockito.mock(TaskInstanceRegistrationRequest.class);
        TMSFeignException ex = Mockito.mock(TMSFeignException.class);
        when(ex.getStatus()).thenReturn(HttpStatus.FORBIDDEN.value());
        when(taskServiceClient.registerTaskInstance(any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(ex);
        assertThrows(TMSException.class, () -> taskServiceAdapter.registerTaskInstance(eq(request), Mockito.any(), Mockito.any(), Mockito.any()));
    }

    @Test
    void registerTaskInstance_serverError() {
        TMSFeignException ex = Mockito.mock(TMSFeignException.class);
        when(ex.getStatus()).thenReturn(HttpStatus.INTERNAL_SERVER_ERROR.value());
        when(taskServiceClient.registerTaskInstance(any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(ex);
        assertThrows(TMSException.class, () -> taskServiceAdapter.registerTaskInstance(
                eq(Mockito.mock(TaskInstanceRegistrationRequest.class)), Mockito.any(), Mockito.any(), Mockito.any()));
    }
}
