package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.constants.TaskServiceConstants;
import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.dto.taskmanager.TaskRegistrationDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.enums.TaskRole;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.integration.adapter.TaskServiceAdapter;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceDeRegistrationRequest;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceDeRegistrationResponse;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;
import com.dpw.ctms.move.mapper.TaskInstanceRegistrationRequestMapper;
import com.dpw.ctms.move.repository.TaskRepository;
import com.dpw.ctms.move.service.ITaskParamService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.IVehicleOperatorService;
import com.dpw.tmsutils.annotation.MethodLog;
import com.dpw.tmsutils.exception.TMSException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TASK_CODE;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TASK_ID;
import static com.dpw.ctms.move.constants.RoleType.CONTROLLER;
import static com.dpw.ctms.move.constants.RoleType.VEHICLE_OPERATION_ROLE_TYPE;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;

@Service
@RequiredArgsConstructor
@Slf4j
public class TaskServiceImpl implements ITaskService {

    private final TaskInstanceRegistrationRequestMapper taskInstanceRegistrationRequestMapper;
    private final TaskRepository taskRepository;
    private final TaskServiceAdapter taskServiceAdapter;
    private final ITaskParamService taskParamService;
    private final IVehicleOperatorService vehicleOperatorService;

    @Override
    public Task findTaskById(Long taskId) {
        return taskRepository.findById(taskId)
                .orElseThrow(() -> {
                    log.error("Task id {} not found", taskId);
                    return new TMSException(DATA_NOT_FOUND.name(), String.format(INVALID_TASK_ID, taskId));
                });
    }

    @Override
    public Task saveTask(Task task) {
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @MethodLog
    public void registerTaskInstances(List<Task> taskList, Map<String, GetOperatorDetailsListResponse> operatorDetailsMap) {
        if (CollectionUtils.isEmpty(taskList)) {
            log.info("Task list is empty, nothing to register");
            return;
        }

        if (operatorDetailsMap == null) {
            log.info("Operator details map is null, using empty map");
            operatorDetailsMap = Collections.emptyMap();
        }

        // Step 1: Extract vehicle operators for all tasks
        Map<String, List<String>> taskVehicleOperatorMap = extractVehicleOperators(taskList);

        // Step 2: Build task registration DTOs using provided operator details
        List<TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO> taskInstanceRegisterDetailsDTOList =
                buildTaskRegistrationDTOs(taskList, taskVehicleOperatorMap, operatorDetailsMap);

        // Step 3: Register tasks and update with registration codes
        registerTasksAndUpdateCodes(taskList, taskInstanceRegisterDetailsDTOList);
    }

    private Map<String, List<String>> extractVehicleOperators(List<Task> taskList) {
        Map<String, List<String>> taskVehicleOperatorMap = new HashMap<>();

        taskList.forEach(task -> {
            List<ParamValueVehicleOperatorDTO> vehicleOperators = taskParamService.getVehicleOperators(task);
            if (!vehicleOperators.isEmpty()) {
                List<String> operatorCodes = vehicleOperators.stream()
                        .map(ParamValueVehicleOperatorDTO::getExternalResourceId)
                        .filter(Objects::nonNull)
                        .toList();
                if (!operatorCodes.isEmpty()) {
                    taskVehicleOperatorMap.put(task.getCode(), operatorCodes);
                }
            }
        });

        return taskVehicleOperatorMap;
    }

    private List<TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO> buildTaskRegistrationDTOs(
            List<Task> taskList,
            Map<String, List<String>> taskVehicleOperatorMap,
            Map<String, GetOperatorDetailsListResponse> operatorDetailsMap) {

        return taskList.stream()
                .map(task -> buildSingleTaskRegistrationDTO(task, taskVehicleOperatorMap, operatorDetailsMap))
                .toList();
    }

    private TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO buildSingleTaskRegistrationDTO(
            Task task,
            Map<String, List<String>> taskVehicleOperatorMap,
            Map<String, GetOperatorDetailsListResponse> operatorDetailsMap) {

        TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO taskDTO =
                taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(task);

        List<TaskRegistrationDTO.RoleAccess> roleAccessList = new ArrayList<>();

        // Add vehicle operator role access
        List<String> vehicleOperatorCodes = taskVehicleOperatorMap.get(task.getCode());
        if (vehicleOperatorCodes != null) {
            addVehicleOperatorRoleAccess(vehicleOperatorCodes, operatorDetailsMap, roleAccessList);
        }

        // Add controller role access
        addControllerRoleAccess(roleAccessList);

        taskDTO.setRoleAccessRequest(new TaskRegistrationDTO.RoleAccessDTO(roleAccessList));
        return taskDTO;
    }

    private void addVehicleOperatorRoleAccess(
            List<String> vehicleOperatorCodes,
            Map<String, GetOperatorDetailsListResponse> operatorDetailsMap,
            List<TaskRegistrationDTO.RoleAccess> roleAccessList) {

        vehicleOperatorCodes.forEach(operatorCode -> {
            String crpCode = fetchCRPCode(operatorCode, operatorDetailsMap);
            if (crpCode != null) {
                VEHICLE_OPERATION_ROLE_TYPE.forEach(roleType ->
                        roleAccessList.add(TaskRegistrationDTO.RoleAccess.builder()
                                .accessTo(crpCode)
                                .accessType(roleType)
                                .role(TaskParamType.VEHICLE_OPERATOR.name())
                                .build())
                );
            } else {
                log.warn("CRP code not found for vehicle operator: {}", operatorCode);
            }
        });
    }

    private void addControllerRoleAccess(List<TaskRegistrationDTO.RoleAccess> roleAccessList) {
        roleAccessList.add(TaskRegistrationDTO.RoleAccess.builder()
                .accessTo(TaskRole.ALL.name())
                .accessType(TaskRole.ALL.name())
                .role(CONTROLLER)
                .build());
    }

    private void registerTasksAndUpdateCodes(
            List<Task> taskList,
            List<TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO> taskInstanceRegisterDetailsDTOList) {

        TaskRegistrationDTO taskRegistrationDTO = TaskRegistrationDTO.builder()
                .tasks(taskInstanceRegisterDetailsDTOList)
                .build();

        TaskInstanceRegistrationRequest request =
                taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(taskRegistrationDTO);

        /**
         * TODO: add the exact hierarchy Identifier using client code
         */
        List<TaskInstanceRegistrationResponse> responses =
                taskServiceAdapter.registerTaskInstance(request, TaskServiceConstants.DEFAULT_TENANT_CODE,
                        TaskServiceConstants.DEFAULT_TENANT_SERVICE_CODE, "CURR_CLIENT_CODE:TMSC0000010#$#CURR_LOC_CODE:LOC1");

        updateTasksWithRegistrationCodes(taskList, responses);
    }

    private void updateTasksWithRegistrationCodes(
            List<Task> taskList,
            List<TaskInstanceRegistrationResponse> responses) {

        Map<String, Task> taskByCode = taskList.stream()
                .collect(Collectors.toMap(Task::getCode, Function.identity()));

        responses.forEach(response -> {
            Task task = taskByCode.get(response.getExtTaskTransactionCode());
            if (task != null) {
                task.setExternalTaskRegistrationCode(response.getTaskRegistrationCode());
            } else {
                log.warn("Task not found for response code: {}", response.getExtTaskTransactionCode());
            }
        });
    }

    private String fetchCRPCode(
            String vehicleOperatorCode,
            Map<String, GetOperatorDetailsListResponse> operatorDetailsMap) {

        GetOperatorDetailsListResponse operatorDetails = operatorDetailsMap.get(vehicleOperatorCode);
        if (operatorDetails == null) {
            log.warn("Operator details not found for code: {}", vehicleOperatorCode);
            return null;
        }

        if (operatorDetails.getCrpDetails() == null) {
            log.warn("CRP details not found for operator: {}", vehicleOperatorCode);
            return null;
        }

        return operatorDetails.getCrpDetails().getCrpUserUUID();
    }

    @Override
    public Task findTaskByCode(String taskCode) {
        return taskRepository.findByCode(taskCode)
                .orElseThrow(() -> {
                    log.error("Task with code {} not found", taskCode);
                    return new TMSException(DATA_NOT_FOUND.name(), String.format(INVALID_TASK_CODE, taskCode));
                });
    }

<<<<<<< HEAD

=======
    @Override
    public void discardTasks(Set<Task> tasks) {
        Set<Task> discardedTasks = tasks.stream()
                .peek(task -> task.setStatus(TaskStatus.DISCARDED))
                .collect(Collectors.toSet());
        TaskInstanceDeRegistrationRequest taskInstanceDeRegistrationRequest = createTaskInstanceDeRegistrationRequest(discardedTasks);
        List<TaskInstanceDeRegistrationResponse> taskInstanceDeRegistrationResponses =
                taskServiceAdapter.deRegisterTaskInstance(taskInstanceDeRegistrationRequest);
        log.info("Task instance de-registration done for task codes {}", taskInstanceDeRegistrationResponses
                .stream().map(TaskInstanceDeRegistrationResponse::getExtTaskTransactionCode)
                .collect(Collectors.toSet()));
    }

    private TaskInstanceDeRegistrationRequest createTaskInstanceDeRegistrationRequest(Set<Task> tasks) {
        List<TaskInstanceDeRegistrationRequest.TaskInstanceDeRegisterDetailsRequest> taskDetails = tasks.stream()
                .filter(Objects::nonNull)
                .map(task -> TaskInstanceDeRegistrationRequest.TaskInstanceDeRegisterDetailsRequest.builder()
                        .taskRegistrationCode(task.getExternalTaskRegistrationCode()) // assuming such a getter exists
                        .build())
                .collect(Collectors.toList());

        return TaskInstanceDeRegistrationRequest.builder()
                .tasks(taskDetails)
                .build();
    }
>>>>>>> 2653ded9ea00a43e8be07fb9b988ca0289039002
}