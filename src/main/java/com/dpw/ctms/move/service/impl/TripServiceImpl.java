package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.mapper.TripViewMapper;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.request.TripListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripListingResponse;
import com.dpw.ctms.move.response.TripViewResponse;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITripDataService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.service.TripFilteringService;
import com.dpw.tmsutils.exception.TMSException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TRIP_ID;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;

@Slf4j
@Service
@RequiredArgsConstructor
public class TripServiceImpl implements ITripService {

    private final TripFilteringService tripFilteringService;
    private final ITripDataService tripDataService;
    private final TripViewMapper tripViewMapper;
    private final TripRepository tripRepository;
    private final IShipmentService shipmentService;
    private final ITaskService taskService;

    /**
     * Lists trips based on the provided trip listing request.
     *
     * @param tripListingRequest the request containing pagination, sorting, and filtering criteria
     * @return a ListResponse containing the list of TripListingResponse objects and total records
     */
    @Override
    public ListResponse<TripListingResponse> listTrips(TripListingRequest tripListingRequest) {
        return tripFilteringService.filterTrips(tripListingRequest);
    }

    @Override
    @Transactional
    public TripViewResponse getTripView(String tripCode) {
        Trip trip = tripDataService.getTripByCodeWithAllDetails(tripCode);
        return tripViewMapper.toResponse(trip);
    }

    @Override
    public Trip findTripById(Long id) {
        return tripRepository.findById(id).orElseThrow(() -> {
            log.error("Trip id {} not found", id);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_TRIP_ID, id)
            );
        });
    }

    @Override
    public Trip saveTrip(Trip trip) {
        return tripRepository.save(trip);
    }
    @Override
    public boolean isTripCancellable(Trip trip) {
        Set<Shipment> shipments = trip.getShipments();
        return shipments.stream().allMatch(s -> s.getStatus().equals(ShipmentStatus.CANCELLED));
    }

    @Override
    public void discardTrips(Set<Trip> trips) {
        Set<Trip> discardedTrips = trips.stream()
                .peek(trip -> trip.setStatus(TripStatus.DISCARDED))
                .collect(Collectors.toSet());

        Set<Shipment> shipments = discardedTrips.stream()
                .filter(Objects::nonNull)
                .flatMap(trip -> trip.getShipments().stream())
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        shipmentService.discardShipments(shipments);



        Set<Task> tasks =  discardedTrips.stream()
                .flatMap(trip -> trip.getStops().stream())
                .filter(Objects::nonNull)
                .flatMap(stop -> stop.getStopTasks().stream())
                .filter(Objects::nonNull)
                .map(StopTask::getTask)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        taskService.discardTasks(tasks);
    }
}
