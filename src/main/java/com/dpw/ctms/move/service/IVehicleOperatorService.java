package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;

import java.util.List;
import java.util.Map;

public interface IVehicleOperatorService {
    Map<String, GetOperatorDetailsListResponse> fetchOperatorDetails(
            Map<String, List<String>> taskVehicleOperatorMap);

    Map<String, GetOperatorDetailsListResponse> populateCrpIdInTransportOrder(TransportOrder transportOrder, List<Task> taskList);
}
