package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;

import java.util.List;
<<<<<<< HEAD
import java.util.Map;
=======
import java.util.Set;
>>>>>>> 2653ded9ea00a43e8be07fb9b988ca0289039002

public interface ITaskService {
    Task findTaskById(Long taskId);
    Task saveTask(Task task);
    void registerTaskInstances(List<Task> taskList, Map<String, GetOperatorDetailsListResponse> operatorDetailsMap);
    Task findTaskByCode(String taskCode);
    void discardTasks(Set<Task> tasks);
}
