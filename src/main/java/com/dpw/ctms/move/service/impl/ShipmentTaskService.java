package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.ShipmentTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.repository.ShipmentTaskRepository;
import com.dpw.ctms.move.service.IShipmentTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ShipmentTaskService implements IShipmentTaskService {
    private final ShipmentTaskRepository shipmentTaskRepository;

    @Override
    @Transactional
    public Set<Task> getTasksByShipments(Set<Shipment> shipments) {
        Set<Long> shipmentIds = shipments.stream()
                .map(Shipment::getId)
                .collect(Collectors.toSet());
        List<ShipmentTask> shipmentTasks = shipmentTaskRepository.findAllByShipmentIdIn(shipmentIds);
        return shipmentTasks.stream().map(ShipmentTask::getTask).collect(Collectors.toSet());
    }
    @Override
    public ShipmentTask saveShipmentTask(ShipmentTask shipmentTask) {
        return shipmentTaskRepository.save(shipmentTask);
    }
}
