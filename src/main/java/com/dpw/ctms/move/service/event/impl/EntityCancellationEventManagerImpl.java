package com.dpw.ctms.move.service.event.impl;

import com.dpw.ctms.move.dto.producer.ShipmentCancellationEventRequestDTO;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.event.IEntityCancellationEventHandler;
import com.dpw.ctms.move.service.event.IEntityCancellationEventManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class EntityCancellationEventManagerImpl implements IEntityCancellationEventManager {
    private final Map<String, IEntityCancellationEventHandler> entityCancellationEventHandlerHashMap = new HashMap<>();

    @Override
    public void register(String entityType, IEntityCancellationEventHandler entityCancellationEventHandler) {
        entityCancellationEventHandlerHashMap.put(entityType, entityCancellationEventHandler);
    }
    @Override
    public void acknowledgeEntityCancellation(ShipmentCancellationEventRequestDTO shipmentCancellationEventRequestDTO) {
        if (Objects.isNull(shipmentCancellationEventRequestDTO)) {
            log.error("Cancellation Event request is null");
        }
        IEntityCancellationEventHandler entityCancellationEventHandler = entityCancellationEventHandlerHashMap.get(shipmentCancellationEventRequestDTO.getEntityType());
        if (Objects.isNull(entityCancellationEventHandler)) {
            log.error(String.format("Entity cancellation event handler not found for %s", shipmentCancellationEventRequestDTO.getEntityType()));
        }
        try {
            IntegratorMessageResponse response = entityCancellationEventHandler.acknowledgeEntityCancellationEvent(shipmentCancellationEventRequestDTO);
            log.info("Successfully processed {} cancellation event: {}", shipmentCancellationEventRequestDTO.getEntityType(), response.isSuccess());
        } catch (Exception e) {
            log.error(String.format("Failed to process event for entity type %s: %s", shipmentCancellationEventRequestDTO.getEntityType(), e.getMessage()), e);
        }
    }
}
