package com.dpw.ctms.move.service.event.impl;


import com.dpw.ctms.move.dto.producer.EventRequestDTO;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.event.IEntityEventHandler;
import com.dpw.ctms.move.service.event.IEntityEventManager;
import com.dpw.tmsutils.annotation.LogExecutionTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@RequiredArgsConstructor
@Service
@Slf4j
@LogExecutionTime
public class EntityEventManagerImpl implements IEntityEventManager {

    private final Map<String, IEntityEventHandler> eventHandlerMap = new HashMap<>();

    @Override
    public void register(String entityType, IEntityEventHandler eventHandler) {
        eventHandlerMap.put(entityType, eventHandler);
    }

    @Override
    public boolean updateStatus(EventRequestDTO requestDto) {
        if (Objects.isNull(requestDto)) {
            log.error("Event request is null");
            return false;
        }
        IEntityEventHandler entityEventHandler = eventHandlerMap.get(requestDto.getEntityType());
        if (Objects.isNull(entityEventHandler)) {
            log.error(String.format("Entity event handler not found for %s", requestDto.getEntityType()));
            return false;
        }
        try {
            IntegratorMessageResponse response = entityEventHandler.updateStatusEvent(requestDto);
            return response != null && response.isSuccess();
        } catch (Exception e) {
            log.error(String.format("Failed to process event for entity type %s: %s", requestDto.getEntityType(), e.getMessage()), e);
            return false;
        }
    }
}
