package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.Shipment;

import java.util.List;
import java.util.Set;

import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.ctms.move.response.ShipmentViewResponse;

public interface IShipmentService {
    ListResponse<ShipmentListingResponse> listShipments(ShipmentListingRequest shipmentListingRequest);
    Shipment findShipmentById(Long id);
    Shipment saveShipment(Shipment entity);
    Shipment findShipmentByCode(String code);
    List<Shipment> getAllByCodes(Set<String> codes);
    ShipmentViewResponse getShipmentView(String shipmentCode);
    void discardShipments(Set<Shipment> shipments);
    boolean areShipmentsCancellable(List<Shipment> shipments);
}
