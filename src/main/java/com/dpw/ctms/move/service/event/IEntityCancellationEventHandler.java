package com.dpw.ctms.move.service.event;

import com.dpw.ctms.move.dto.producer.ShipmentCancellationEventRequestDTO;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;

public interface IEntityCancellationEventHandler {
    /**TODO make the DTO generic to support cancellation of different entities**/
    IntegratorMessageResponse acknowledgeEntityCancellationEvent(ShipmentCancellationEventRequestDTO shipmentCancellationEventRequestDTO);
}
