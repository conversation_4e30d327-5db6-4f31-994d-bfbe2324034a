package com.dpw.ctms.move.service.event.impl;

import com.dpw.ctms.move.dto.producer.EventRequestDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.request.common.IntegratorMessageHeader;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.request.common.MessageRequest;
import com.dpw.ctms.move.request.message.ShipmentStatusUpdateMessage;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.IEventProcessorService;
import com.dpw.ctms.move.service.event.IEntityEventHandler;
import com.dpw.ctms.move.service.event.IEntityEventManager;
import com.dpw.tmsutils.annotation.LogExecutionTime;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.dpw.ctms.move.constants.MoveConstants.SOURCE_MOVE;
import static com.dpw.ctms.move.enums.MessageActionType.SHIPMENT_STATUS_UPDATE;

@RequiredArgsConstructor
@Service
@Slf4j
@LogExecutionTime
public class ShipmentEventHandlerImpl implements IEntityEventHandler<Shipment> {

    private final IEventProcessorService<ShipmentStatusUpdateMessage> eventProcessorService;
    private final IEntityEventManager entityEventManger;

    @Value("${kafka.producer.topics.ctms-move-shipment-events.name}")
    private String shipmentStatusUpdateTopicName;

    @PostConstruct
    public void register() {
        /**TODO not use StateMachineEntityType here rather define and use from KafkaMessageEntityType enum**/
        entityEventManger.register(StateMachineEntityType.SHIPMENT.name(), this);
    }

    @Override
    public IntegratorMessageResponse updateStatusEvent(EventRequestDTO<Shipment> eventRequest) {
        Shipment originalEntity = eventRequest.getOriginalEntity();
        Shipment updatedEntity = eventRequest.getUpdatedEntity();
        try {
            log.info("Processing shipment status update event after status update={}→{}",
                    originalEntity.getStatus(), updatedEntity.getStatus());

            ShipmentStatusUpdateMessage shipmentStatusUpdateMessage = ShipmentStatusUpdateMessage.builder()
                    .currentStatus(updatedEntity.getStatus().name())
                    .previousStatus(originalEntity.getStatus().name())
                    .shipmentCode(updatedEntity.getCode())
                    .extShipmentCode(updatedEntity.getCode())
                    .externalConsignmentId(updatedEntity.getExternalConsignmentId())
                    .pickupTime(updatedEntity.getActualPickupAt())
                    .deliveryTime(updatedEntity.getActualDeliveryAt())
                    .updatedBy(updatedEntity.getUpdatedBy())
                    .updatedAt(updatedEntity.getUpdatedAt())
                    .eventType(eventRequest.getEventType())
                    .comments(eventRequest.getComments())
                    .build();

            IntegratorMessageRequest<ShipmentStatusUpdateMessage> messageRequest =
                    IntegratorMessageRequest.<ShipmentStatusUpdateMessage>builder()
                            .transactionContext(IntegratorMessageHeader.builder()
                                    .action(SHIPMENT_STATUS_UPDATE.name())
                                    .dateTime(System.currentTimeMillis())
                                    .source(SOURCE_MOVE)
                                    .topic(shipmentStatusUpdateTopicName)
                                    .build())
                            .message(MessageRequest.<ShipmentStatusUpdateMessage>builder().
                                    item(shipmentStatusUpdateMessage).build())
                            .build();

            IntegratorMessageResponse response =  eventProcessorService.processRequest(SHIPMENT_STATUS_UPDATE.name(),messageRequest);
            log.info("Successfully published Kafka message for shipment status update: shipment={}, status={}→{}",
                    updatedEntity.getCode(), originalEntity.getStatus(), updatedEntity.getStatus());

            return response;

        } catch (Exception e) {
            log.error("Failed to publish Kafka message for shipment status update: shipment={}, status={}→{}, error={}",
                    updatedEntity.getCode(), originalEntity.getStatus(), updatedEntity.getStatus(), e.getMessage(), e);
        }
        return   IntegratorMessageResponse.builder().build();
    }
}
