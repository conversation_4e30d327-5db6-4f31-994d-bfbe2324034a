package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.mapper.IEntityRelationshipMapper;
import com.dpw.ctms.move.mapper.TransportOrderMapper;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.request.TransportOrderListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TransportOrderListingResponse;
import com.dpw.ctms.move.response.TransportOrderDetailsResponse;
import com.dpw.ctms.move.response.TransportOrderResponse;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITransportOrderService;
import com.dpw.ctms.move.service.IVehicleOperatorService;
import com.dpw.ctms.move.service.TransportOrderFilteringService;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.TMSExceptionErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TRANSPORT_ORDER_ID;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.TRANSPORT_ORDER_DB_PERSISTENCE_FAILED;
import static com.dpw.ctms.move.constants.ResponseMessageConstants.TRANSPORT_ORDER_CREATION_SUCCESS_MESSAGE;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransportOrderServiceImpl implements ITransportOrderService {
    private final TransportOrderMapper transportOrderMapper;
    private final TransportOrderRepository transportOrderRepository;
    private final List<IEntityRelationshipMapper> relationshipMappers;
    private final TransportOrderFilteringService transportOrderFilteringService;
    private final ITaskService taskService;
    private final IVehicleOperatorService vehicleOperatorService;

    @Override
    @Transactional
    public TransportOrderResponse createTransportOrderFTLFulfilment(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest) {
        try {
            // Step 1: Create and process transport order
            TransportOrderDTO transportOrderDTO = transportOrderMapper.toDTO(transportOrderFTLCreateRequest);
            TransportOrder transportOrder = transportOrderMapper.toEntity(transportOrderDTO);
            processTransportOrder(transportOrderDTO, transportOrder);

            // Step 2: Extract tasks from transport order
            List<Task> tasks = extractTasks(transportOrder);

            // Step 3: Populate crpId in VehicleOperatorResource entities and get operator details
            Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                    populateCrpIdInTransportOrder(transportOrder, tasks);

            // Step 4: Register tasks using the same operator details
            registerTaskInstances(tasks, operatorDetailsMap);

            // Step 5: Save transport order after both operations are complete
            TransportOrder persistedTransportOrder = saveTransportOrder(transportOrder);

            return buildTransportOrderResponse(persistedTransportOrder);
        }
        catch (DataAccessException dataAccessException) {
            throw new TMSException(TMSExceptionErrorCode.INTERNAL_ERROR.name(), TRANSPORT_ORDER_DB_PERSISTENCE_FAILED,
                    Map.of("rootCause", dataAccessException.getClass().getSimpleName(), "errorDetails", dataAccessException.getMessage()));
        }
    }

    @Override
    public ListResponse<TransportOrderListingResponse> listTransportOrders(TransportOrderListingRequest transportOrderListingRequest) {
        return transportOrderFilteringService.filterTransportOrders(
                transportOrderListingRequest
        );
    }

    @Override
    public TransportOrder findTransportOrderById(Long id) {
        return transportOrderRepository.findById(id).orElseThrow(() -> {
            log.error("Transport Order with id {} not found", id);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_TRANSPORT_ORDER_ID, id)
            );
        });
    }

    private TransportOrderResponse buildTransportOrderResponse(TransportOrder transportOrder) {
        return TransportOrderResponse.builder()
                .transportOrderCode(transportOrder.getCode())
                .isSuccess(true)
                .message(TRANSPORT_ORDER_CREATION_SUCCESS_MESSAGE)
                .build();
    }

    private void processTransportOrder(TransportOrderDTO transportOrderDTO, TransportOrder transportOrder) {
        for (IEntityRelationshipMapper entityRelationshipMapper : relationshipMappers) {
            entityRelationshipMapper.mapRelationships(transportOrderDTO, transportOrder);
        }
    }

    @Override
    public TransportOrder saveTransportOrder(TransportOrder transportOrder) {
        TransportOrder persistedTransportOrder = transportOrderRepository.save(transportOrder);
        log.info("Transport Order and its related entities successfully persisted: {}", persistedTransportOrder);
        return persistedTransportOrder;
    }

    private List<Task> extractTasks(TransportOrder transportOrder) {
        return transportOrder.getTrips().stream()
                .filter(Objects::nonNull)
                .flatMap(trip -> trip.getStops().stream())
                .filter(Objects::nonNull)
                .flatMap(stop -> stop.getStopTasks().stream())
                .filter(Objects::nonNull)
                .map(StopTask::getTask)
                .filter(Objects::nonNull)
                .toList();
    }

    private void populateCrpIdInTransportOrder(TransportOrder transportOrder, List<Task> tasks) {
        if (transportOrder == null) {
            log.warn("Transport order is null, cannot populate crpId");
            return;
        }

        if (tasks == null || tasks.isEmpty()) {
            log.info("No tasks found, skipping crpId population");
            return;
        }

        try {
            vehicleOperatorService.populateCrpIdInTransportOrder(transportOrder, tasks);
            log.info("Successfully populated crpId for transport order: {}", transportOrder.getCode());
        } catch (Exception e) {
            log.error("Failed to populate crpId for transport order: {}", transportOrder.getCode(), e);
            throw new TMSException(TMSExceptionErrorCode.INTERNAL_ERROR.name(),
                    "Failed to populate crpId in vehicle operator resources",
                    Map.of("transportOrderCode", transportOrder.getCode(), "errorDetails", e.getMessage()));
        }
    }

    private void registerTaskInstances(List<Task> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            log.info("No tasks found, skipping task registration");
            return;
        }

        try {
            taskService.registerTaskInstances(tasks);
            log.info("Successfully registered {} task instances", tasks.size());
        } catch (Exception e) {
            log.error("Failed to register task instances for {} tasks", tasks.size(), e);
            throw new TMSException(TMSExceptionErrorCode.INTERNAL_ERROR.name(),
                    "Failed to register task instances",
                    Map.of("taskCount", String.valueOf(tasks.size()), "errorDetails", e.getMessage()));
        }
    }

    @Override
    @Transactional(readOnly = true)
    public TransportOrderDetailsResponse getTransportOrderDetails(String code) {
        TransportOrder transportOrder = transportOrderRepository.findByCode(code)
                .orElseThrow(() -> new TMSException(INVALID_REQUEST.name(), "Transport order  not found with code: " + code));
        return transportOrderMapper.mapToDetailsResponse(transportOrder);
    }
}
