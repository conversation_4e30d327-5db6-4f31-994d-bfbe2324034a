package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;
import com.dpw.ctms.move.mapper.IEntityRelationshipMapper;
import com.dpw.ctms.move.mapper.TransportOrderMapper;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.request.TransportOrderDiscardRequest;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.request.TransportOrderListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TransportOrderListingResponse;
import com.dpw.ctms.move.response.TransportOrderDetailsResponse;
import com.dpw.ctms.move.response.TransportOrderResponse;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITransportOrderService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.service.IVehicleOperatorService;
import com.dpw.ctms.move.service.TransportOrderFilteringService;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.TMSExceptionErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TRANSPORT_ORDER_CODE;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TRANSPORT_ORDER_ID;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.TRANSPORT_ORDER_DB_PERSISTENCE_FAILED;
import static com.dpw.ctms.move.constants.ResponseMessageConstants.TRANSPORT_ORDER_CREATION_SUCCESS_MESSAGE;
import static com.dpw.ctms.move.constants.ResponseMessageConstants.TRANSPORT_ORDER_DISCARD_NOT_ALLOWED_MESSAGE;
import static com.dpw.ctms.move.constants.ResponseMessageConstants.TRANSPORT_ORDER_DISCARD_SUCCESS_MESSAGE;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransportOrderServiceImpl implements ITransportOrderService {
    private final TransportOrderMapper transportOrderMapper;
    private final TransportOrderRepository transportOrderRepository;
    private final List<IEntityRelationshipMapper> relationshipMappers;
    private final TransportOrderFilteringService transportOrderFilteringService;
    private final ITaskService taskService;
    private final ITripService tripService;
    private final IShipmentService shipmentService;
    private final IVehicleOperatorService vehicleOperatorService;

    @Override
    @Transactional
    public TransportOrderResponse createTransportOrderFTLFulfilment(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest) {
        try {
            // Step 1: Create and process transport order
            TransportOrderDTO transportOrderDTO = transportOrderMapper.toDTO(transportOrderFTLCreateRequest);
            TransportOrder transportOrder = transportOrderMapper.toEntity(transportOrderDTO);
            processTransportOrder(transportOrderDTO, transportOrder);

            // Step 2: Extract tasks from transport order
            List<Task> tasks = extractTasks(transportOrder);

            // Step 3: Populate crpId in VehicleOperatorResource entities and get operator details
            Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                    populateCrpIdInTransportOrder(transportOrder, tasks);

            // Step 4: Register tasks using the same operator details
            registerTaskInstances(tasks, operatorDetailsMap);

            // Step 5: Save transport order after both operations are complete
            TransportOrder persistedTransportOrder = saveTransportOrder(transportOrder);

            return buildTransportOrderResponse(persistedTransportOrder);
        }
        catch (DataAccessException dataAccessException) {
            throw new TMSException(TMSExceptionErrorCode.INTERNAL_ERROR.name(), TRANSPORT_ORDER_DB_PERSISTENCE_FAILED,
                    Map.of("rootCause", dataAccessException.getClass().getSimpleName(), "errorDetails", dataAccessException.getMessage()));
        }
    }

    @Override
    public ListResponse<TransportOrderListingResponse> listTransportOrders(TransportOrderListingRequest transportOrderListingRequest) {
        return transportOrderFilteringService.filterTransportOrders(
                transportOrderListingRequest
        );
    }

    @Override
    public TransportOrder findTransportOrderById(Long id) {
        return transportOrderRepository.findById(id).orElseThrow(() -> {
            log.error("Transport Order with id {} not found", id);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_TRANSPORT_ORDER_ID, id)
            );
        });
    }

    @Override
    public TransportOrder findTransportOrderByCode(String code) {
        return transportOrderRepository.findByCode(code).orElseThrow(() -> {
            log.error("Transport Order with code {} not found", code);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_TRANSPORT_ORDER_CODE, code)
            );
        });
    }

    @Override
    @Transactional
    public TransportOrderResponse discardTransportOrder(String code, TransportOrderDiscardRequest transportOrderDiscardRequest) {
        if (!canTransportOrderBeDiscarded(code)) {
            log.warn("Transport order with code {} cannot be discarded", code);
            return TransportOrderResponse.builder()
                    .transportOrderCode(code)
                    .isSuccess(false)
                    .message(TRANSPORT_ORDER_DISCARD_NOT_ALLOWED_MESSAGE)
                    .build();
        }
        else {
            TransportOrder transportOrder = findTransportOrderByCode(code);
            discardTransportOrderInternal(transportOrder);
            return TransportOrderResponse.builder()
                    .transportOrderCode(code)
                    .isSuccess(true)
                    .message(TRANSPORT_ORDER_DISCARD_SUCCESS_MESSAGE)
                    .build();
        }
    }

    private boolean canTransportOrderBeDiscarded(String transportOrderCode) {
        TransportOrder transportOrder = findTransportOrderByCode(transportOrderCode);
        return transportOrder.getStatus().equals(TransportOrderStatus.ASSIGNED);
    }

    private void discardTransportOrderInternal(TransportOrder transportOrder) {
        transportOrder.setStatus(TransportOrderStatus.DISCARDED);
        Set<Trip> trips = Optional.ofNullable(transportOrder.getTrips())
                .orElse(Collections.emptySet())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        tripService.discardTrips(trips);

        Set<Shipment> shipments = Optional.ofNullable(transportOrder.getShipments())
                .orElse(Collections.emptySet())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        shipmentService.discardShipments(shipments);
        saveTransportOrder(transportOrder);
    }

    private TransportOrderResponse buildTransportOrderResponse(TransportOrder transportOrder) {
        return TransportOrderResponse.builder()
                .transportOrderCode(transportOrder.getCode())
                .isSuccess(true)
                .message(TRANSPORT_ORDER_CREATION_SUCCESS_MESSAGE)
                .build();
    }

    private void processTransportOrder(TransportOrderDTO transportOrderDTO, TransportOrder transportOrder) {
        for (IEntityRelationshipMapper entityRelationshipMapper : relationshipMappers) {
            entityRelationshipMapper.mapRelationships(transportOrderDTO, transportOrder);
        }
    }

    @Override
    public TransportOrder saveTransportOrder(TransportOrder transportOrder) {
        TransportOrder persistedTransportOrder = transportOrderRepository.save(transportOrder);
        log.info("Transport Order and its related entities successfully persisted: {}", persistedTransportOrder);
        return persistedTransportOrder;
    }

    private List<Task> extractTasks(TransportOrder transportOrder) {
        return transportOrder.getTrips().stream()
                .filter(Objects::nonNull)
                .flatMap(trip -> trip.getStops().stream())
                .filter(Objects::nonNull)
                .flatMap(stop -> stop.getStopTasks().stream())
                .filter(Objects::nonNull)
                .map(StopTask::getTask)
                .filter(Objects::nonNull)
                .toList();
    }

    private Map<String, GetOperatorDetailsListResponse> populateCrpIdInTransportOrder(TransportOrder transportOrder, List<Task> tasks) {
        if (transportOrder == null) {
            log.info("Transport order is null, cannot populate crpId");
            return Collections.emptyMap();
        }

        if (CollectionUtils.isEmpty(tasks)) {
            log.info("No tasks found, skipping crpId population");
            return Collections.emptyMap();
        }

        try {
            Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                    vehicleOperatorService.populateCrpIdInTransportOrder(transportOrder, tasks);
            log.info("Successfully populated crpId for transport order: {}", transportOrder.getCode());
            return operatorDetailsMap;
        } catch (Exception e) {
            log.error("Failed to populate crpId for transport order: {}", transportOrder.getCode(), e);
            throw new TMSException(TMSExceptionErrorCode.INTERNAL_ERROR.name(),
                    "Failed to populate crpId in vehicle operator resources",
                    Map.of("transportOrderCode", transportOrder.getCode(), "errorDetails", e.getMessage()));
        }
    }

    private void registerTaskInstances(List<Task> tasks, Map<String, GetOperatorDetailsListResponse> operatorDetailsMap) {
        if (tasks == null || tasks.isEmpty()) {
            log.info("No tasks found, skipping task registration");
            return;
        }

        if (operatorDetailsMap == null) {
            log.info("Operator details map is null, proceeding with empty map");
            operatorDetailsMap = Collections.emptyMap();
        }

        try {
            taskService.registerTaskInstances(tasks, operatorDetailsMap);
            log.info("Successfully registered {} task instances", tasks.size());
        } catch (Exception e) {
            log.error("Failed to register task instances for {} tasks", tasks.size(), e);
            throw new TMSException(TMSExceptionErrorCode.INTERNAL_ERROR.name(),
                    "Failed to register task instances",
                    Map.of("taskCount", String.valueOf(tasks.size()), "errorDetails", e.getMessage()));
        }
    }

    @Override
    @Transactional(readOnly = true)
    public TransportOrderDetailsResponse getTransportOrderDetails(String code) {
        TransportOrder transportOrder = transportOrderRepository.findByCode(code)
                .orElseThrow(() -> new TMSException(INVALID_REQUEST.name(), "Transport order  not found with code: " + code));
        return transportOrderMapper.mapToDetailsResponse(transportOrder);
    }

    @Override
    public boolean isTransportOrderFTLFulfilmentCancellable(TransportOrder transportOrder) {
        Set<Trip> trips = transportOrder.getTrips();
        return trips.stream().allMatch(t -> t.getStatus().equals(TripStatus.CANCELLED));
    }

    @Override
    public boolean isTransportOrderLTLFulfilmentCancellable(TransportOrder transportOrder) {
        Set<Shipment> shipments = transportOrder.getShipments();
        return shipments.stream().allMatch(s ->
                s.getTrip() == null && s.getStatus().equals(ShipmentStatus.CANCELLED));
    }
}
