package com.dpw.ctms.move.service.event;

import com.dpw.ctms.move.dto.producer.ShipmentCancellationEventRequestDTO;

public interface IEntityCancellationEventManager {
    void register(String entityType, IEntityCancellationEventHandler shipmentCancellationEventHandler);
    /**TOD<PERSON> change DTO to accept any entity cancellation instead of shipment **/
    void acknowledgeEntityCancellation(ShipmentCancellationEventRequestDTO shipmentCancellationEventRequestDTO);
}
