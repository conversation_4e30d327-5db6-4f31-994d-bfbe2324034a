package com.dpw.ctms.move.service;

import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.enums.Tenant;
import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.request.EntityDocumentRequest;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import com.dpw.ctms.move.response.PreSignedUrlResponse;
import com.dpw.ctms.move.response.DocumentDownloadResponse;
import com.dpw.ctms.move.response.DocumentErrorResponse;
import com.dpw.ctms.move.response.EntityDocumentResponse;
import com.dpw.ctms.move.response.FileDownloadPreSignedUrlResponse;

import java.util.List;

public interface IDocumentsService {
    DocumentDownloadResponse downloadTripBolDocument(String tripCode, Tenant tenant);
    PreSignedUrlResponse getPreSignedUrl();
    void findAndUpdate(PreSignedUrlEvent preSignedUrlEvent);
    void findAndUpdate(DeliveryTaskDocumentDTO deliveryTaskDocumentDTO);
    DocumentErrorResponse getAllErrors(List<String> fileIdentifiers);
    EntityDocumentResponse getDocumentsByEntity(List<EntityDocumentRequest> entityRequests);
    FileDownloadPreSignedUrlResponse getFileDownloadUrls(List<String> externalDocumentIdentifiers);
}
