package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.integration.adapter.ResourceServiceAdapter;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.integration.request.resource.GetOperatorListRequest;
import com.dpw.ctms.move.service.IVehicleOperatorService;
import com.dpw.ctms.move.service.impl.TaskParamService;
import com.dpw.tmsutils.exception.TMSException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleOperatorServiceImpl implements IVehicleOperatorService {
    private final ResourceServiceAdapter resourceServiceAdapter;
    private final TaskParamService taskParamService;

    @Override
    public Map<String, GetOperatorDetailsListResponse> fetchOperatorDetails(
            Map<String, List<String>> taskVehicleOperatorMap) {

        Set<String> allOperatorIds = taskVehicleOperatorMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toSet());

        if (allOperatorIds.isEmpty()) {
            log.warn("No vehicle operators found for tasks");
            return Collections.emptyMap();
        }

        try {
            List<Long> operatorIdsAsLong = allOperatorIds.stream()
                    .map(Long::valueOf)
                    .toList();

            GetOperatorListRequest request = GetOperatorListRequest.builder()
                    .filter(GetOperatorListRequest.GetOperatorListFilter.builder()
                            .ids(operatorIdsAsLong)
                            .build())
                    .pagination(Pagination.builder()
                            .pageNo(0)
                            .pageSize(allOperatorIds.size())
                            .build())
                    .build();

            return resourceServiceAdapter.getOperatorDetailsMap(request);

        } catch (NumberFormatException e) {
            log.error("Invalid operator ID format in task vehicle operators: {}", allOperatorIds, e);
            throw new TMSException("INVALID_OPERATOR_ID", "Invalid operator ID format");
        }
    }

    @Override
    public Map<String, GetOperatorDetailsListResponse> populateCrpIdInTransportOrder(TransportOrder transportOrder, List<Task> taskList) {
        if (taskList == null || taskList.isEmpty()) {
            log.warn("Task list is empty, cannot populate crpId");
            return Collections.emptyMap();
        }

        if (transportOrder == null) {
            log.warn("Transport order is null, cannot populate crpId");
            return Collections.emptyMap();
        }

        // Step 1: Extract vehicle operators for all tasks
        Map<String, List<String>> taskVehicleOperatorMap = extractVehicleOperators(taskList);

        if (taskVehicleOperatorMap.isEmpty()) {
            log.info("No vehicle operators found in tasks, skipping crpId population");
            return Collections.emptyMap();
        }

        // Step 2: Fetch operator details in batch
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap = fetchOperatorDetails(taskVehicleOperatorMap);

        // Step 3: Populate crpId in VehicleOperatorResource entities
        populateCrpIdInVehicleOperatorResources(transportOrder, operatorDetailsMap);

        // Step 4: Return operator details map for reuse in task registration
        return operatorDetailsMap;
    }

    private Map<String, List<String>> extractVehicleOperators(List<Task> taskList) {
        Map<String, List<String>> taskVehicleOperatorMap = new HashMap<>();

        taskList.forEach(task -> {
            if (task == null) {
                log.warn("Null task found in task list, skipping");
                return;
            }

            List<ParamValueVehicleOperatorDTO> vehicleOperators = taskParamService.getVehicleOperators(task);
            if (!vehicleOperators.isEmpty()) {
                List<String> operatorCodes = vehicleOperators.stream()
                        .map(ParamValueVehicleOperatorDTO::getExternalResourceId)
                        .filter(Objects::nonNull)
                        .toList();
                if (!operatorCodes.isEmpty()) {
                    taskVehicleOperatorMap.put(task.getCode(), operatorCodes);
                }
            }
        });

        return taskVehicleOperatorMap;
    }

    private void populateCrpIdInVehicleOperatorResources(
            TransportOrder transportOrder,
            Map<String, GetOperatorDetailsListResponse> operatorDetailsMap) {

        if (transportOrder.getTrips() == null) {
            log.info("No trips found in transport order");
            return;
        }

        transportOrder.getTrips().forEach(trip -> {
            if (trip == null) {
                log.warn("Null trip found in transport order, skipping");
                return;
            }

            if (trip.getVehicleOperatorResources() != null) {
                trip.getVehicleOperatorResources().forEach(vehicleOperatorResource -> {
                    if (vehicleOperatorResource == null) {
                        log.warn("Null vehicle operator resource found, skipping");
                        return;
                    }

                    String crpId = extractCrpIdFromOperatorDetails(
                            vehicleOperatorResource.getExternalResourceId(), operatorDetailsMap);
                    vehicleOperatorResource.setCrpId(crpId);
                    log.info("Populated crpId {} for vehicle operator resource {}",
                            crpId, vehicleOperatorResource.getExternalResourceId());
                });
            }
        });
    }

    private String extractCrpIdFromOperatorDetails(
            String externalResourceId,
            Map<String, GetOperatorDetailsListResponse> operatorDetailsMap) {

        if (externalResourceId == null) {
            log.info("External resource ID is null, cannot extract crpId");
            return null;
        }

        // Find operator details by external resource ID
        GetOperatorDetailsListResponse operatorDetails = operatorDetailsMap.values().stream()
                .filter(details -> externalResourceId.equals(details.getId() != null ? details.getId().toString() : null))
                .findFirst()
                .orElse(null);

        if (operatorDetails == null) {
            log.info("Operator details not found for external resource ID: {}", externalResourceId);
            return null;
        }

        if (operatorDetails.getCrpDetails() == null) {
            log.info("CRP details not found for operator with external resource ID: {}", externalResourceId);
            return null;
        }

        return operatorDetails.getCrpDetails().getCrpUserUUID();
    }

}
