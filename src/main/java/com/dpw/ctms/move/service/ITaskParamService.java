package com.dpw.ctms.move.service;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.entity.Task;

import java.util.List;
import java.util.Optional;

public interface ITaskParamService {

    Optional<ParamValueShipmentDTO> getShipmentCode(Task task);
    List<ParamValueVehicleOperatorDTO> getVehicleOperators(Task task);
}
