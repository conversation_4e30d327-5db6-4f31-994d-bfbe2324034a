package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.helper.ShipmentCancellationHelper;
import com.dpw.ctms.move.helper.TaskCancellationHelper;
import com.dpw.ctms.move.service.IShipmentCancellationCoordinatorService;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.strategy.fulfilment.IFulfillmentCancellationStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class ShipmentCancellationCoordinatorService implements IShipmentCancellationCoordinatorService {

    private final ShipmentCancellationHelper shipmentCancellationHelper;
    private final TaskCancellationHelper taskCancellationHelper;
    private final List<IFulfillmentCancellationStrategy> cancellationStrategies;
    private final IShipmentService shipmentService;

    public boolean cancelShipmentsAndRelatedEntities(List<Shipment> shipments) {
        if (!shipmentService.areShipmentsCancellable(shipments)) return false;

        shipmentCancellationHelper.cancelShipments(shipments);
        taskCancellationHelper.discardTasksByShipments(shipments);

        Set<Shipment> shipmentSet = new HashSet<>(shipments);
        for (IFulfillmentCancellationStrategy strategy : cancellationStrategies) {
            if (strategy.supports(shipmentSet)) {
                strategy.cancel(shipmentSet);
            }
        }
        return true;
    }
}

