package com.dpw.ctms.move.service.event.impl;

import com.dpw.ctms.move.dto.producer.EventRequestDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.request.common.IntegratorMessageHeader;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.request.common.MessageRequest;
import com.dpw.ctms.move.request.message.TaskStatusUpdateMessage;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.IEventProcessorService;
import com.dpw.ctms.move.service.event.IEntityEventHandler;
import com.dpw.ctms.move.service.event.IEntityEventManager;
import com.dpw.tmsutils.annotation.LogExecutionTime;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.dpw.ctms.move.constants.MoveConstants.SOURCE_MOVE;
import static com.dpw.ctms.move.enums.MessageActionType.TASK_STATUS_UPDATE;

@RequiredArgsConstructor
@Service
@Slf4j
@LogExecutionTime
public class TaskEventHandlerImpl implements IEntityEventHandler<Task> {

    private final IEventProcessorService<TaskStatusUpdateMessage> eventProcessorService;
    private final IEntityEventManager entityEventManger;

    @Value("${kafka.producer.topics.ctms-move-task-events.name}")
    private String taskStatusUpdateTopicName;

    @PostConstruct
    public void register() {
        /**TODO not use StateMachineEntityType here rather define and use from KafkaMessageEntityType enum**/
        entityEventManger.register(StateMachineEntityType.TASK.name(), this);
    }

    @Override
    public IntegratorMessageResponse updateStatusEvent(EventRequestDTO<Task> eventRequest) {
        Task originalEntity = eventRequest.getOriginalEntity();
        Task updatedEntity = eventRequest.getUpdatedEntity();
        try {
            log.info("Processing task status update event after status update={}→{}",
                    originalEntity.getStatus(), updatedEntity.getStatus());

            TaskStatusUpdateMessage taskStatusUpdateMessage = TaskStatusUpdateMessage.builder()
                    .currentStatus(updatedEntity.getStatus().name())
                    .previousStatus(originalEntity.getStatus().name())
                    .taskCode(updatedEntity.getCode())
                    .extTaskCode(updatedEntity.getCode())
                    .taskRegistrationCode(updatedEntity.getExternalTaskRegistrationCode())
                    .startTime(updatedEntity.getActualStartAt())
                    .endTime(updatedEntity.getActualEndAt())
                    .updatedBy(updatedEntity.getUpdatedBy())
                    .updatedAt(updatedEntity.getUpdatedAt())
                    .eventType(eventRequest.getEventType())
                    .comments(eventRequest.getComments())
                    .build();

            IntegratorMessageRequest<TaskStatusUpdateMessage> messageRequest =
                    IntegratorMessageRequest.<TaskStatusUpdateMessage>builder()
                            .transactionContext(IntegratorMessageHeader.builder()
                                    .action(TASK_STATUS_UPDATE.name())
                                    .dateTime(System.currentTimeMillis())
                                    .source(SOURCE_MOVE)
                                    .topic(taskStatusUpdateTopicName)
                                    .build())
                            .message(MessageRequest.<TaskStatusUpdateMessage>builder().
                                    item(taskStatusUpdateMessage).build())
                            .build();

            IntegratorMessageResponse response = eventProcessorService.processRequest(TASK_STATUS_UPDATE.name(),messageRequest);
            log.info("Successfully published Kafka message for task status update: task={}, status={}→{}",
                    updatedEntity.getCode(), originalEntity.getStatus(), updatedEntity.getStatus());

            return response;

        } catch (Exception e) {
            log.error("Failed to publish Kafka message for rip status update: task={}, status={}→{}, error={}",
                    updatedEntity.getCode(), originalEntity.getStatus(), updatedEntity.getStatus(), e.getMessage(), e);
        }
        return IntegratorMessageResponse.builder().build();
    }
}
