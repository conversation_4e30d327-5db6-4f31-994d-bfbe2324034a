package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.request.TransportOrderDiscardRequest;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.request.TransportOrderListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TransportOrderDetailsResponse;
import com.dpw.ctms.move.response.TransportOrderListingResponse;
import com.dpw.ctms.move.response.TransportOrderResponse;

public interface ITransportOrderService {
    TransportOrderResponse createTransportOrderFTLFulfilment(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest);
    TransportOrder saveTransportOrder(TransportOrder transportOrder);
    ListResponse<TransportOrderListingResponse> listTransportOrders(TransportOrderListingRequest transportOrderListingRequest);
    TransportOrderDetailsResponse getTransportOrderDetails(String code);
    TransportOrder findTransportOrderById(Long id);
    boolean isTransportOrderFTLFulfilmentCancellable(TransportOrder transportOrder);
    boolean isTransportOrderLTLFulfilmentCancellable(TransportOrder transportOrder);
    TransportOrder findTransportOrderByCode(String code);
    TransportOrderResponse discardTransportOrder(String code, TransportOrderDiscardRequest transportOrderDiscardRequest);

}
