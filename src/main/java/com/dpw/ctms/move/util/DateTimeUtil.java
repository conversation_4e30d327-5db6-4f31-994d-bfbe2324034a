package com.dpw.ctms.move.util;

import com.dpw.ctms.move.dto.DateTimeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.DateTimeException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

import static com.dpw.ctms.move.constants.PropertyConstants.DATE_FORMAT;
import static com.dpw.ctms.move.constants.PropertyConstants.DATETIME_FORMAT;
import static com.dpw.ctms.move.constants.PropertyConstants.DEFAULT_TIME_ZONE_ID;

@Slf4j
public class DateTimeUtil {
    
    private DateTimeUtil() {}
    
    public static DateTimeDTO fromEpochMillis(Long epochMillis) {
        if (epochMillis == null) {
            return null;
        }
        Instant instant = Instant.ofEpochMilli(epochMillis);
        ZoneId zoneId = ZoneId.of(DEFAULT_TIME_ZONE_ID);
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);
        // Format with timezone abbreviation (e.g., "2023-12-01T10:30 GMT")
        String isoString = zonedDateTime.format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));
        
        return DateTimeDTO.builder()
                .epoch(epochMillis)
                .iso(isoString)
                .timeZoneId(DEFAULT_TIME_ZONE_ID)
                .build();
    }

    public static DateTimeDTO fromEpochMillis(Long epochMillis, String timeZoneId) {
        if (epochMillis == null) {
            return null;
        }
        try {
            Instant instant = Instant.ofEpochMilli(epochMillis);
            ZoneId zoneId;
            if (StringUtils.isEmpty(timeZoneId)) {
                String fullZoneId = ZoneId.SHORT_IDS.get(timeZoneId);
                zoneId = ZoneId.of(fullZoneId);
            } else {
                zoneId = ZoneOffset.UTC;
            }
            
            ZonedDateTime zonedDateTime = instant.atZone(zoneId);
            String formattedDate = zonedDateTime.format(DateTimeFormatter.ofPattern(DATETIME_FORMAT));

            return DateTimeDTO.builder()
                    .epoch(epochMillis)
                    .iso(formattedDate)
                    .timeZoneId(timeZoneId)
                    .build();
        } catch (DateTimeException e) {
            log.error("Error converting epochMillis to DateTimeDTO: {}", e);
            // If invalid timezone, fallback to UTC
            Instant instant = Instant.ofEpochMilli(epochMillis);
            ZonedDateTime zonedDateTime = instant.atZone(ZoneOffset.UTC);
            return DateTimeDTO.builder()
                    .epoch(epochMillis)
                    .iso(zonedDateTime.format(DateTimeFormatter.ofPattern(DATETIME_FORMAT)))
                    .timeZoneId(timeZoneId)
                    .build();
        }
    }

    public static DateTimeDTO fromEpochMillisToDate(Long epochMillis, String timeZoneId) {
        if (epochMillis == null) {
            return null;
        }
        try {
            Instant instant = Instant.ofEpochMilli(epochMillis);
            ZoneId zoneId;
            if (StringUtils.isEmpty(timeZoneId)) {
                String fullZoneId = ZoneId.SHORT_IDS.get(timeZoneId);
                zoneId = ZoneId.of(fullZoneId);
            } else {
                zoneId = ZoneOffset.UTC;
            }
            ZonedDateTime zonedDateTime = instant.atZone(zoneId);
            String dateWithTimezone = zonedDateTime.format(DateTimeFormatter.ofPattern(DATE_FORMAT));

            return DateTimeDTO.builder()
                    .epoch(epochMillis)
                    .iso(dateWithTimezone)
                    .timeZoneId(timeZoneId)
                    .build();
        } catch (DateTimeException e) {
            log.error("Error converting epochMillis to DateTimeDTO: {}", e);
            // If invalid timezone, fallback to UTC
            Instant instant = Instant.ofEpochMilli(epochMillis);
            ZonedDateTime zonedDateTime = instant.atZone(ZoneOffset.UTC);
            String dateWithTimezone = zonedDateTime.format(DateTimeFormatter.ofPattern(DATE_FORMAT));
            return DateTimeDTO.builder()
                    .epoch(epochMillis)
                    .iso(dateWithTimezone)
                    .timeZoneId(timeZoneId)
                    .build();
        }
    }


    
    public static DateTimeDTO fromIsoString(String isoString) {
        if (isoString == null || isoString.trim().isEmpty()) {
            return new DateTimeDTO();
        }
        
        try {
            LocalDateTime utcDateTime = LocalDateTime.parse(isoString, DateTimeFormatter.ISO_LOCAL_DATE);
            Instant instant = utcDateTime.toInstant(ZoneOffset.UTC);
            Long epochMillis = instant.toEpochMilli();
            
            return DateTimeDTO.builder()
                    .epoch(epochMillis)
                    .iso(isoString)
                    .build();
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid ISO format: " + isoString, e);
        }
    }
    
    public static Long toEpochMillis(String isoString) {
        if (isoString == null || isoString.trim().isEmpty()) {
            return null;
        }
        
        try {
            LocalDateTime utcDateTime = LocalDateTime.parse(isoString, DateTimeFormatter.ISO_LOCAL_DATE);
            Instant instant = utcDateTime.toInstant(ZoneOffset.UTC);
            return instant.toEpochMilli();
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid ISO format: " + isoString, e);
        }
    }
    
    public static String toIsoString(Long epochMillis) {
        if (epochMillis == null) {
            return null;
        }
        
        Instant instant = Instant.ofEpochMilli(epochMillis);
        LocalDateTime utcDateTime = LocalDateTime.ofInstant(instant, ZoneOffset.UTC);
        return utcDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE);
    }
}