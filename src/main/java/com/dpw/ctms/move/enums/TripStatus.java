package com.dpw.ctms.move.enums;

public enum TripStatus implements DisplayableStatusEnum {
    CREATED("Created"),
    IN_PROGRESS("In Progress"),
    COMPLETED("Completed"),
    COMPLETED_WITH_EXCEPTIONS("Completed with exceptions"),
    CANCELLED("Cancelled"),
    CLOSED("Closed"),
    DISCARDED("Discarded");

    private final String displayText;

    TripStatus(String displayText) {
        this.displayText = displayText;
    }

    public String getDisplayName() {
        return this.displayText;
    }

    public static TripStatus fromDisplayName(String displayText) {
        if (displayText == null) {
            return null;
        }
        for (TripStatus status : TripStatus.values()) {
            if (status.displayText.equalsIgnoreCase(displayText)) {
                return status;
            }
        }
        return null;
    }

}
