package com.dpw.ctms.move.helper;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.enums.ShipmentLifecycleEvent;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.tmsutils.threadlocal.TenantContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ShipmentCancellationHelper {

    private final StateMachineServiceRegistry stateMachineServiceRegistry;

    public void cancelShipments(List<Shipment> shipments) {
        IStateMachineService<?> stateMachineService =
                stateMachineServiceRegistry.getService(StateMachineEntityType.SHIPMENT);

        for (Shipment shipment : shipments) {
            stateMachineService.handleEvent(
                    TenantContext.getCurrentTenant(),
                    ShipmentLifecycleEvent.CANCELLED.name(),
                    shipment.getId()
            );
        }
    }
}

