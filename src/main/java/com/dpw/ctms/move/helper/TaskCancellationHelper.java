package com.dpw.ctms.move.helper;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.service.IShipmentTaskService;
import com.dpw.ctms.move.service.ITaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
@RequiredArgsConstructor
public class TaskCancellationHelper {

    private final IShipmentTaskService shipmentTaskService;
    private final ITaskService taskService;
    public void discardTasksByShipments(List<Shipment> shipments) {
        Set<Task> tasks = shipmentTaskService.getTasksByShipments(new HashSet<>(shipments));
        taskService.discardTasks(tasks);
    }
}
