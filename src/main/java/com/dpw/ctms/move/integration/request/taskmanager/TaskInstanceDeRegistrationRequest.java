package com.dpw.ctms.move.integration.request.taskmanager;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TaskInstanceDeRegistrationRequest {
    List<TaskInstanceDeRegisterDetailsRequest> tasks;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskInstanceDeRegisterDetailsRequest {
        String taskRegistrationCode;
    }
}
