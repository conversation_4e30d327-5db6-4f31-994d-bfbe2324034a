package com.dpw.ctms.move.integration.adapter;

import com.dpw.ctms.move.integration.feignClient.TaskServiceClient;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceDeRegistrationRequest;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceDeRegistrationResponse;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.integration.response.TaskListResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class TaskServiceAdapter extends BaseServiceAdapter {

    private final TaskServiceClient taskServiceClient;

    public List<TaskInstanceRegistrationResponse> registerTaskInstance (
            TaskInstanceRegistrationRequest taskInstanceRegistrationRequest,
            String tenantCode, String serviceCode, String hierarchyIdentifier) {
        TaskListResponse<TaskInstanceRegistrationResponse> response =
                execute("registerTaskInstance", () -> taskServiceClient
                        .registerTaskInstance(taskInstanceRegistrationRequest, tenantCode, serviceCode,
                                hierarchyIdentifier).getBody());
        return response.getList();
    }
    public List<TaskInstanceDeRegistrationResponse> deRegisterTaskInstance (
            TaskInstanceDeRegistrationRequest taskInstanceDeRegistrationRequest) {
        TaskListResponse<TaskInstanceDeRegistrationResponse> response =
                execute("deRegisterTaskInstance", () -> taskServiceClient
                        .deRegisterTaskInstance(taskInstanceDeRegistrationRequest).getBody());
        return response.getList();
    }

    @Override
    public String getServiceName() {
        return "Task Service";
    }
}
