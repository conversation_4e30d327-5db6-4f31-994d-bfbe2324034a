package com.dpw.ctms.move.integration.request.taskmanager;

import com.dpw.ctms.move.dto.TimeDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TaskInstanceRegistrationRequest {
    List<TaskInstanceRegisterDetailsRequest> tasks;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskInstanceRegisterDetailsRequest {
        String extTaskTransactionCode;
        String extTaskMasterCode;
        RoleAccessRequest roleAccessRequest;
        ExpectedDateRange expectedDateRange;
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExpectedDateRange {
        TimeDTO start;
        TimeDTO end;
    }

    @Data
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    public static class RoleAccessRequest {
        private List<RoleAccess> roleAccesses;
    }

    @Data
    @Builder
    public static class RoleAccess {
        private String role;
        private String accessTo;
        private String accessType;
    }

}
