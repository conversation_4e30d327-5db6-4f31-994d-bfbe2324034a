package com.dpw.ctms.move.integration.feignClient;

import com.dpw.ctms.move.config.FeignConfig;
import com.dpw.ctms.move.constants.TaskServiceConstants;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceDeRegistrationRequest;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceDeRegistrationResponse;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.integration.response.TaskListResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

import static com.dpw.ctms.move.constants.TaskServiceConstants.Endpoints.TASK_INSTANCE_DE_REGISTER;

@FeignClient(name = "taskService", url = "${tms.service-url.task-service:http://localhost:8081}", configuration = FeignConfig.class)
public interface TaskServiceClient {

    @PostMapping(value = TaskServiceConstants.Endpoints.TASK_INSTANCE_REGISTER,
            produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    ResponseEntity<TaskListResponse<TaskInstanceRegistrationResponse>> registerTaskInstance(
            @RequestBody TaskInstanceRegistrationRequest taskInstanceRegistrationRequest,
            @RequestHeader("tenantCode") String tenantCode, @RequestHeader("tenantServiceCode") String serviceCode,
            @RequestHeader("hierarchyIdentifier") String hierarchyIdentifier
    );

    @PostMapping(value = TASK_INSTANCE_DE_REGISTER, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    ResponseEntity<TaskListResponse<TaskInstanceDeRegistrationResponse>> deRegisterTaskInstance(
            @RequestBody TaskInstanceDeRegistrationRequest taskInstanceDeRegistrationRequest
    );
}