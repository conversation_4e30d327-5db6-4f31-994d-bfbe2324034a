package com.dpw.ctms.move.dto.consumer;

import com.dpw.ctms.move.dto.TimeDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ToString
public class IntegratorTaskMessageRequestDTO<T> {
    private IntegratorHeaderDTO transactionContext;
    private Map<String, Object> metadata;
    private MessageRequestDTO<T> message;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    @SuperBuilder
    public static class MessageRequestDTO<T> {
        private IntegratorTaskDetailsRequestDTO taskDetails;
        private T percolatedRecords;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder
    public static class IntegratorTaskDetailsRequestDTO {
        private String taskRegistrationCode;
        private String taskTransactionCode;
        private String taskName;
        private String status;
        private ActualTimeRange actualTimeRange;
        private Map<String, Object> additionalInfo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder
    public static class ActualTimeRange {
        private TimeDTO from;
        private TimeDTO to;
    }
}
