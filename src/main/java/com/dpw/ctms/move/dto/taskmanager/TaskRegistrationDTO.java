package com.dpw.ctms.move.dto.taskmanager;

import com.dpw.ctms.move.dto.TimeDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Builder
public class TaskRegistrationDTO {
    List<TaskInstanceRegisterDetailsDTO> tasks;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaskInstanceRegisterDetailsDTO {
        String extTaskTransactionCode;
        String extTaskMasterCode;
        String tenantCode;
        RoleAccessDTO roleAccessRequest;
        ExpectedDateRange expectedDateRange;
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    public static class ExpectedDateRange {
        TimeDTO start;
        TimeDTO end;
    }

    @Data
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    public static class RoleAccessDTO {
        private List<RoleAccess> roleAccesses;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RoleAccess {
        private String role;
        private String accessTo;
        private String accessType;
    }
}
