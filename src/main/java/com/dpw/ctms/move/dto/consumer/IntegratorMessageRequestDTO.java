package com.dpw.ctms.move.dto.consumer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IntegratorMessageRequestDTO<T> {
    private IntegratorHeaderDTO transactionContext;
    private Message<T> message;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Message<T> {
        private T item;
    }
}
