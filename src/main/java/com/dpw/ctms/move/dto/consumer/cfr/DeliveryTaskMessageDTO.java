package com.dpw.ctms.move.dto.consumer.cfr;

import com.dpw.ctms.move.dto.consumer.IntegratorTaskMessageRequestDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@AllArgsConstructor
@Data
@SuperBuilder
public class DeliveryTaskMessageDTO extends IntegratorTaskMessageRequestDTO<
        DeliveryTaskMessageDTO.PercolatedRecordDTO> {

    @AllArgsConstructor
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class PercolatedRecordDTO {
        private Long arrivalTime;
        private Long unloadingCompletionTime;
        private Long departureTime;
        private String bolIdentifier;
        private String bolUploadTime;
    }
}