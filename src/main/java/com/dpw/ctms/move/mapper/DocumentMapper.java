package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.document.DocumentDTO;
import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import com.dpw.ctms.move.response.EntityDocumentResponse;
import com.dpw.ctms.move.enums.DocumentStatus;
import com.dpw.ctms.move.enums.DocumentOperationType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.MappingTarget;

/**
 * Maps {@link Document} ↔ {@link DocumentDTO}.
 */
@Mapper(componentModel = "spring")
public interface DocumentMapper {

    @Mappings({
            @Mapping(target = "documentType", source = "documentType"),
            @Mapping(target = "checksum",     source = "checksum"),
            @Mapping(target = "fileIdentifier",  source = "fileIdentifier"),
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "status", source = "status"),
            @Mapping(target = "documentOperationType", source = "documentOperationType"),
    })
    DocumentDTO toDTO(Document document);

    @Mappings({
            @Mapping(target = "documentType", source = "documentType"),
            @Mapping(target = "checksum", source = "checksum"),
            @Mapping(target = "fileIdentifier", source = "fileIdentifier"),
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "status", source = "status"),
            @Mapping(target = "documentOperationType", source = "documentOperationType"),
            @Mapping(target = "clientIdentifier", ignore = true),
            @Mapping(target = "asyncMappingUUID", ignore = true),
            @Mapping(target = "fileName", ignore = true),
            @Mapping(target = "fileSize", ignore = true),
            @Mapping(target = "fileType", ignore = true),
            @Mapping(target = "presignedDownloadUrl", ignore = true),
            @Mapping(target = "fileMetadata", ignore = true)
    })
    Document toEntity(DocumentDTO dto);

    @Mappings({
            @Mapping(target = "clientIdentifier", source = "clientIdentifier"),
            @Mapping(target = "asyncMappingUUID", source = "fileKey"),
            @Mapping(target = "fileIdentifier", source = "fileIdentifier"),
            @Mapping(target = "fileName", source = "fileName"),
            @Mapping(target = "fileSize", source = "fileSize"),
            @Mapping(target = "fileType", source = "fileType"),
            @Mapping(target = "presignedDownloadUrl", source = "presignedDownloadUrl"),
            @Mapping(target = "fileMetadata", source = "fileMetadata"),
            @Mapping(target = "status", expression = "java(com.dpw.ctms.move.enums.DocumentStatus.INACTIVE)"),
            @Mapping(target = "documentOperationType", expression = "java(com.dpw.ctms.move.enums.DocumentOperationType.UPLOAD)"),
            @Mapping(target = "entityId", ignore = true),
            @Mapping(target = "entityType", ignore = true),
            @Mapping(target = "documentType", ignore = true),
            @Mapping(target = "checksum", ignore = true),
    })
    Document fromPreSignedUrlEvent(PreSignedUrlEvent event);

    @Mappings({
            @Mapping(target = "clientIdentifier", source = "clientIdentifier"),
            @Mapping(target = "asyncMappingUUID", source = "fileKey"),
            @Mapping(target = "fileIdentifier", source = "fileIdentifier"),
            @Mapping(target = "fileName", source = "fileName"),
            @Mapping(target = "fileSize", source = "fileSize"),
            @Mapping(target = "fileType", source = "fileType"),
            @Mapping(target = "presignedDownloadUrl", source = "presignedDownloadUrl"),
            @Mapping(target = "fileMetadata", source = "fileMetadata"),
            @Mapping(target = "documentOperationType", expression = "java(com.dpw.ctms.move.enums.DocumentOperationType.UPLOAD)"),
            @Mapping(target = "entityId", ignore = true),
            @Mapping(target = "entityType", ignore = true),
            @Mapping(target = "documentType", ignore = true),
            @Mapping(target = "status", ignore = true),
            @Mapping(target = "checksum", ignore = true),
    })
    void updateDocumentFromPreSignedUrlEvent(@MappingTarget Document document, PreSignedUrlEvent event);

    @Mappings({
            @Mapping(target = "asyncMappingUUID", source = "asyncMappingUUID"),
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "documentOperationType", source = "operationType"),
            @Mapping(target = "status", expression = "java(com.dpw.ctms.move.enums.DocumentStatus.INACTIVE)"),
    })
    Document fromDeliveryTaskDocumentDTO(DeliveryTaskDocumentDTO dto);

    @Mappings({
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "fileIdentifier", ignore = true),
    })
    void updateDocumentFromDeliveryTaskDTO(@MappingTarget Document document, DeliveryTaskDocumentDTO dto);

    // Overloaded updateDocument methods with direct mappings
    @Mappings({
            @Mapping(target = "clientIdentifier", source = "clientIdentifier"),
            @Mapping(target = "asyncMappingUUID", source = "fileKey"),
            @Mapping(target = "fileIdentifier", source = "fileIdentifier"),
            @Mapping(target = "fileName", source = "fileName"),
            @Mapping(target = "fileSize", source = "fileSize"),
            @Mapping(target = "fileType", source = "fileType"),
            @Mapping(target = "presignedDownloadUrl", source = "presignedDownloadUrl"),
            @Mapping(target = "fileMetadata", source = "fileMetadata"),
            @Mapping(target = "documentOperationType", expression = "java(com.dpw.ctms.move.enums.DocumentOperationType.UPLOAD)"),
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "entityId", ignore = true),
            @Mapping(target = "entityType", ignore = true),
            @Mapping(target = "documentType", ignore = true),
            @Mapping(target = "status", ignore = true),
            @Mapping(target = "checksum", ignore = true),
    })
    void updateDocument(@MappingTarget Document document, PreSignedUrlEvent event);

    @Mappings({
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "fileIdentifier", ignore = true),
            @Mapping(target = "asyncMappingUUID", ignore = true),
            @Mapping(target = "documentOperationType", source = "operationType"),
    })
    void updateDocument(@MappingTarget Document document, DeliveryTaskDocumentDTO dto);

    // Overloaded createDocument methods with direct mappings
    @Mappings({
            @Mapping(target = "clientIdentifier", source = "clientIdentifier"),
            @Mapping(target = "asyncMappingUUID", source = "fileKey"),
            @Mapping(target = "fileIdentifier", source = "fileIdentifier"),
            @Mapping(target = "fileName", source = "fileName"),
            @Mapping(target = "fileSize", source = "fileSize"),
            @Mapping(target = "fileType", source = "fileType"),
            @Mapping(target = "presignedDownloadUrl", source = "presignedDownloadUrl"),
            @Mapping(target = "fileMetadata", source = "fileMetadata"),
            @Mapping(target = "status", expression = "java(com.dpw.ctms.move.enums.DocumentStatus.INACTIVE)"),
            @Mapping(target = "documentOperationType", expression = "java(com.dpw.ctms.move.enums.DocumentOperationType.UPLOAD)"),
            @Mapping(target = "entityId", ignore = true),
            @Mapping(target = "entityType", ignore = true),
            @Mapping(target = "documentType", ignore = true),
            @Mapping(target = "checksum", ignore = true),
    })
    Document createDocument(PreSignedUrlEvent event);

    @Mappings({
            @Mapping(target = "asyncMappingUUID", source = "asyncMappingUUID"),
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "documentOperationType", source = "operationType"),
            @Mapping(target = "status", expression = "java(com.dpw.ctms.move.enums.DocumentStatus.INACTIVE)"),
    })
    Document createDocument(DeliveryTaskDocumentDTO dto);

    @Mappings({
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "entityCode", source = "entityId"),
            @Mapping(target = "asyncMappingUUID", source = "asyncMappingUUID"),
            @Mapping(target = "fileName", source = "fileName"),
            @Mapping(target = "fileSize", source = "fileSize"),
            @Mapping(target = "fileType", source = "fileType"),
            @Mapping(target = "externalDocumentIdentifier", source = "fileIdentifier")
    })
    EntityDocumentResponse.FileDetail toFileDetail(Document document);

}
