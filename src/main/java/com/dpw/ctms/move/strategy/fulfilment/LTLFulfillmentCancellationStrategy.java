package com.dpw.ctms.move.strategy.fulfilment;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TransportOrderLifecycleEvent;
import com.dpw.ctms.move.service.ITransportOrderService;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.tmsutils.threadlocal.TenantContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class LTLFulfillmentCancellationStrategy implements IFulfillmentCancellationStrategy {

    private final ITransportOrderService transportOrderService;
    private final StateMachineServiceRegistry stateMachineServiceRegistry;

    @Override
    public boolean supports(Set<Shipment> shipments) {
        return shipments.stream()
                .anyMatch(shipment -> shipment.getTrip() == null && shipment.getTransportOrder() != null);
    }

    @Override
    public void cancel(Set<Shipment> shipments) {
        Set<TransportOrder> transportOrders = shipments.stream()
                .map(Shipment::getTransportOrder)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        IStateMachineService<?> stateMachineService =
                stateMachineServiceRegistry.getService(StateMachineEntityType.TRANSPORT_ORDER);

        for (TransportOrder to : transportOrders) {
            if (transportOrderService.isTransportOrderLTLFulfilmentCancellable(to)) {
                stateMachineService.handleEvent(
                        TenantContext.getCurrentTenant(),
                        TransportOrderLifecycleEvent.CANCEL_TRANSPORT_ORDER.name(),
                        to.getId()
                );
            }
        }
    }
}
