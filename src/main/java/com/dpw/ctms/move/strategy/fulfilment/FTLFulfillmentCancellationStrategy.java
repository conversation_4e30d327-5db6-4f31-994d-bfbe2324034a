package com.dpw.ctms.move.strategy.fulfilment;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TransportOrderLifecycleEvent;
import com.dpw.ctms.move.enums.TripLifecycleEvent;
import com.dpw.ctms.move.service.ITransportOrderService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.tmsutils.threadlocal.TenantContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class FTLFulfillmentCancellationStrategy implements IFulfillmentCancellationStrategy {

    private final ITripService tripService;
    private final ITransportOrderService transportOrderService;
    private final StateMachineServiceRegistry stateMachineServiceRegistry;

    @Override
    public boolean supports(Set<Shipment> shipments) {
        return shipments.stream().anyMatch(shipment -> shipment.getTrip() != null);
    }

    @Override
    public void cancel(Set<Shipment> shipments) {
        Set<Trip> trips = shipments.stream()
                .map(Shipment::getTrip)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        IStateMachineService<?> tripStateMachineService =
                stateMachineServiceRegistry.getService(StateMachineEntityType.TRIP);
        IStateMachineService<?> toStateMachine =
                stateMachineServiceRegistry.getService(StateMachineEntityType.TRANSPORT_ORDER);

        for (Trip trip : trips) {
            if (tripService.isTripCancellable(trip)) {
                tripStateMachineService.handleEvent(
                        TenantContext.getCurrentTenant(),
                        TripLifecycleEvent.CANCEL_TRIP.name(),
                        trip.getId()
                );
            }
        }

        Set<TransportOrder> transportOrders = trips.stream()
                .map(Trip::getTransportOrder)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        for (TransportOrder to : transportOrders) {
            if (transportOrderService.isTransportOrderFTLFulfilmentCancellable(to)) {
                toStateMachine.handleEvent(
                        TenantContext.getCurrentTenant(),
                        TransportOrderLifecycleEvent.CANCEL_TRANSPORT_ORDER.name(),
                        to.getId()
                );
            }
        }
    }
}

