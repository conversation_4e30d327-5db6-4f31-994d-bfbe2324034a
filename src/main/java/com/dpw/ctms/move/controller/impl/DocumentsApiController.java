package com.dpw.ctms.move.controller.impl;

import com.dpw.ctms.move.controller.DocumentsApi;
import com.dpw.ctms.move.enums.Tenant;
import com.dpw.ctms.move.request.EntityDocumentRequest;
import com.dpw.ctms.move.request.EntityDocumentListRequest;
import com.dpw.ctms.move.request.DocumentErrorRequest;
import com.dpw.ctms.move.response.PreSignedUrlResponse;
import com.dpw.ctms.move.response.DocumentDownloadResponse;
import com.dpw.ctms.move.response.DocumentErrorResponse;
import com.dpw.ctms.move.response.EntityDocumentResponse;
import com.dpw.ctms.move.response.FileDownloadPreSignedUrlResponse;
import com.dpw.ctms.move.service.IDocumentsService;
import com.dpw.tmsutils.threadlocal.TenantContext;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@Validated
public class DocumentsApiController implements DocumentsApi {

    private final IDocumentsService documentService;

    @Override
    public ResponseEntity<DocumentDownloadResponse> downloadTripBolDocument(String tripCode) {

        return ResponseEntity.ok(documentService.downloadTripBolDocument(tripCode, Tenant.valueOf(TenantContext.getCurrentTenant())));
    }

    @Override
    public ResponseEntity<PreSignedUrlResponse> getPreSignedUrl() {
        return ResponseEntity.ok(documentService.getPreSignedUrl());
    }

    @Override
    public ResponseEntity<DocumentErrorResponse> getDocumentErrors(DocumentErrorRequest request) {
        return ResponseEntity.ok(documentService.getAllErrors(request.getFileIdentifiers()));
    }

    @Override
    public ResponseEntity<EntityDocumentResponse> getDocumentsByEntity(EntityDocumentListRequest request) {
        return ResponseEntity.ok(documentService.getDocumentsByEntity(request.getEntityRequests()));
    }

    @Override
    public ResponseEntity<FileDownloadPreSignedUrlResponse> getFileDownloadUrls(List<String> externalDocumentIdentifiers) {
        return ResponseEntity.ok(documentService.getFileDownloadUrls(externalDocumentIdentifiers));
    }
}
