package com.dpw.ctms.move.controller.impl;

import com.dpw.ctms.move.controller.TransportOrderApi;
import com.dpw.ctms.move.request.TransportOrderDiscardRequest;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.request.TransportOrderListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TransportOrderDetailsResponse;
import com.dpw.ctms.move.response.TransportOrderListingResponse;
import com.dpw.ctms.move.response.TransportOrderResponse;
import com.dpw.ctms.move.service.ITransportOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.OK;

@RequiredArgsConstructor
@RestController
@Validated
public class TransportOrderApiController implements TransportOrderApi {
    private final ITransportOrderService transportOrderService;

    @Override
    public ResponseEntity<TransportOrderResponse> createTransportOrderFTLFulfilment(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest) {
        TransportOrderResponse transportOrderResponse = transportOrderService.createTransportOrderFTLFulfilment(transportOrderFTLCreateRequest);
        return ResponseEntity.status(CREATED).body(transportOrderResponse);
    }

    @Override
    public ResponseEntity<ListResponse<TransportOrderListingResponse>> listTransportOrders(TransportOrderListingRequest transportOrderListingRequest) {
        return ResponseEntity.status(OK).body(transportOrderService.listTransportOrders(transportOrderListingRequest));
    }

    @Override
    public ResponseEntity<TransportOrderDetailsResponse> getTransportOrderDetails(String code) {
        TransportOrderDetailsResponse response = transportOrderService.getTransportOrderDetails(code);
        return ResponseEntity.status(OK).body(response);
    }

    @Override
    public ResponseEntity<TransportOrderResponse> discardTransportOrder(String code,
                                                                        TransportOrderDiscardRequest transportOrderDiscardRequest) {
        TransportOrderResponse response = transportOrderService.discardTransportOrder(code, transportOrderDiscardRequest);
        return ResponseEntity.status(OK).body(response);
    }
}
