package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.request.TransportOrderDiscardRequest;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.request.TransportOrderListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TransportOrderDetailsResponse;
import com.dpw.ctms.move.response.TransportOrderListingResponse;
import com.dpw.ctms.move.response.TransportOrderResponse;
import com.dpw.tmsutils.annotation.ApiLog;
import com.dpw.tmsutils.schemaobjects.TMSErrorResponseSo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/v1/transport-orders")
public interface TransportOrderApi {
    @PostMapping(value = "/ftl-fulfilment", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Create transport order containing trips")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Successfully created the transport order with trips"),
            @ApiResponse(responseCode = "422", description = "Unable to create the transport order", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            }
            )
    })
    @ApiLog
    ResponseEntity<TransportOrderResponse> createTransportOrderFTLFulfilment(
            @Valid
            @RequestBody
            @Parameter(name = "transportOrderFTLCreateRequest", required = true, description = "Request to create transport order with trips")
            TransportOrderFTLCreateRequest transportOrderFTLCreateRequest);


    @PostMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Transport Order listing")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully listed the transport orders")
    })
    @ApiLog
    ResponseEntity<ListResponse<TransportOrderListingResponse>> listTransportOrders(
            @Valid
            @RequestBody
            @Parameter(name = "TransportOrderListingRequest", required = true, description = "Request to list transport orders")
            TransportOrderListingRequest transportOrderListingRequest);

    @GetMapping(value = "/{code}/view", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get transport order details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved transport order details"),
            @ApiResponse(responseCode = "404", description = "Transport order not found", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            })
    })
    @ApiLog
    ResponseEntity<TransportOrderDetailsResponse> getTransportOrderDetails(
            @PathVariable("code")
            @Parameter(name = "code", required = true, description = "Transport order code")
            String code);


    @PostMapping(value = "/{code}/discard", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Discard Transport Order")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully discarded transport order"),
            @ApiResponse(responseCode = "422", description = "Unable to discard transport order", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            })
    })
    @ApiLog
    ResponseEntity<TransportOrderResponse> discardTransportOrder(
            @PathVariable("code")
            @Parameter(name = "code", required = true, description = "Transport order code")
            String code,
            @Valid
            @RequestBody
            @Parameter(name = "transportOrderDiscardRequest", required = true, description = "Request to discard transport order")
            TransportOrderDiscardRequest transportOrderDiscardRequest);

}
