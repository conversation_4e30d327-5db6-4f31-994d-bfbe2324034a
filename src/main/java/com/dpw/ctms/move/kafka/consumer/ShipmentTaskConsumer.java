package com.dpw.ctms.move.kafka.consumer;

import com.dpw.ctms.move.dto.consumer.IntegratorTaskMessageRequestDTO;
import com.dpw.tmsutils.annotation.OverrideKafkaTenantStrategy;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.dpw.ctms.move.dto.consumer.IntegratorMessageRequestDTO;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ShipmentTaskConsumer {

    private final TaskActionHandlerRegistry taskActionHandlerRegistry;
    @KafkaListener(
            groupId = "${kafka.consumer.shipment-task-acknowledgement.consumer-group-id:dpw-task-execution-results-dev}",
            topics = "${kafka.consumer.shipment-task-acknowledgement.topic:dpw-task-execution-results-dev}",
            containerFactory = "${kafka.consumer.shipment-task-acknowledgement.container-factory:shipmentTaskContainerFactory}",
            autoStartup = "${kafka.consumer.shipment-task-acknowledgement.enable:false}")
    public void handlePayload(ConsumerRecord<String, String> record) {
        try {
            String jsonMessage = record.value();
            log.debug("Received message from topic: {}, partition: {}, offset: {}",
                    record.topic(), record.partition(), record.offset());
            IntegratorTaskMessageRequestDTO message = ObjectMapperUtil.getObjectMapper().readValue(jsonMessage,
                    new TypeReference<IntegratorTaskMessageRequestDTO>() {
                    });
            log.info("Received the Event from task service having event: {}", message);
            if (message == null) return;
            String type = message.getMessage().getTaskDetails().getTaskName();
            TaskActionHandler handler = taskActionHandlerRegistry.getHandler(type);
            log.info("Dispatching action: {} to handler: {}", type, handler.getClass().getName());
            handler.handle(message);
        } catch (Exception e) {
            log.error("Error while processing message from topic: {}, partition: {}, offset: {}, message: {}",
                    record.topic(), record.partition(), record.offset(), record.value(), e);
        }
    }
}