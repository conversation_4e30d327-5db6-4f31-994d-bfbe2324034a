package com.dpw.ctms.move.kafka.consumer;

import com.dpw.ctms.move.dto.consumer.IntegratorMessageRequestDTO;
import com.dpw.ctms.move.dto.consumer.ShipmentCancellationMessageRequestDTO;
import com.dpw.tmsutils.annotation.OverrideKafkaTenantStrategy;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
@OverrideKafkaTenantStrategy
public class ShipmentCancellationConsumer {
    private final ShipmentEventHandlerRegistry shipmentEventHandlerRegistry;

    @KafkaListener(
            groupId = "${kafka.consumer.shipment-cancellation.consumer-group-id:ctms-planner-consignment-cancellation-local}",
            topics = "${kafka.consumer.shipment-cancellation.topic:ctms-planner-consignment-cancellation}",
            containerFactory = "${kafka.consumer.shipment-cancellation.container-factory:shipmentCancellationContainerFactory}",
            autoStartup = "${kafka.consumer.shipment-cancellation.enable:true}")
    public void handlePayload(ConsumerRecord<String, String> record) {
        try {
            String jsonMessage = record.value();
            log.debug("Received message from topic: {}, partition: {}, offset: {}",
                    record.topic(), record.partition(), record.offset());
            IntegratorMessageRequestDTO<ShipmentCancellationMessageRequestDTO> message = ObjectMapperUtil.getObjectMapper().readValue(jsonMessage,
                    new TypeReference<>() {
                    });
            TenantContext.setCurrentTenant("CFR");
            log.info("Received Shipment Cancellation Event from Planner service having event: {}", message);
            if (message == null) return;
            String action = message.getTransactionContext().getAction();
            ShipmentEventHandler handler = shipmentEventHandlerRegistry.getHandler(action);
            log.info("Dispatching action: {} to handler: {}", action, handler.getClass().getName());
            handler.handle(message);
        } catch (Exception e) {
            log.error("Error while processing message from topic: {}, partition: {}, offset: {}, message: {}",
                    record.topic(), record.partition(), record.offset(), record.value(), e);
        }
    }
}
