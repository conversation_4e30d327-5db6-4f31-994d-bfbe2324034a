package com.dpw.ctms.move.kafka.consumer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ShipmentEventHandlerRegistry {

    private final Map<String, ShipmentEventHandler> registry = new HashMap<>();

    public ShipmentEventHandlerRegistry(List<ShipmentEventHandler> handlers) {
        for (ShipmentEventHandler handler : handlers) {
            registry.put(handler.getSupportedAction(), handler);
        }
    }

    public ShipmentEventHandler getHandler(String action) {
        ShipmentEventHandler handler = registry.get(action);
        if (handler == null) {
            log.error("No handler found for action: {}", action);
        }
        return handler;
    }
}
