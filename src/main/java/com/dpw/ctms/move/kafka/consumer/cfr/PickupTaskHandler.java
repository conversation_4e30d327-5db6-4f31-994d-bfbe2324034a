package com.dpw.ctms.move.kafka.consumer.cfr;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorTaskMessageRequestDTO;
import com.dpw.ctms.move.dto.consumer.cfr.PickupTaskMessageDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.ShipmentLifecycleEvent;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.kafka.consumer.TaskActionHandler;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.ctms.move.util.EnumUtils;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class PickupTaskHandler implements TaskActionHandler<PickupTaskMessageDTO.PercolatedRecordDTO> {

    private final ITaskService taskService;
    private final IShipmentService shipmentService;
    private final ITripService tripService;
    private final StateMachineServiceRegistry stateMachineServiceRegistry;
    private final TaskPercolationHelperUtil taskPercolationHelperUtil;

    @Override
    @Transactional
    public void handle(IntegratorTaskMessageRequestDTO<PickupTaskMessageDTO.PercolatedRecordDTO> message) {
        String taskCode = message.getMessage().getTaskDetails().getTaskTransactionCode();
        TaskStatus taskStatus = EnumUtils.getEnumFromString(TaskStatus.class,
                message.getMessage().getTaskDetails().getStatus(), true);
        Task task = taskService.findTaskByCode(taskCode);
        ParamValueShipmentDTO shipmentDTO = taskPercolationHelperUtil.extractShipmentDTOFromTask(task, taskCode);
        Shipment shipment = shipmentService.findShipmentByCode(shipmentDTO.getCode());
        Trip trip = shipment.getTrip();

        Shipment initialShipment = ObjectMapperUtil.getObjectMapper().convertValue(shipment, Shipment.class);
        Task initialTask = ObjectMapperUtil.getObjectMapper().convertValue(task, Task.class);
        Trip initialTrip = ObjectMapperUtil.getObjectMapper().convertValue(trip, Trip.class);

        //update task status and details
        taskPercolationHelperUtil.updateTask(task, taskStatus, message);
        PickupTaskMessageDTO.PercolatedRecordDTO percolatedRecords = ObjectMapperUtil.getObjectMapper().convertValue(
                message.getMessage().getPercolatedRecords(), PickupTaskMessageDTO.PercolatedRecordDTO.class);
        updateShipment(shipment, percolatedRecords, taskStatus);
        updateTrip(shipment.getTrip(), percolatedRecords, taskStatus);
        taskPercolationHelperUtil.updateTransportOrder(shipment.getTransportOrder());

        taskPercolationHelperUtil.triggerEvent(initialTask, initialShipment, initialTrip);
    }

    private void updateShipment(Shipment shipment, PickupTaskMessageDTO.PercolatedRecordDTO percolatedRecords,
                                TaskStatus taskStatus) {
        if (taskStatus == TaskStatus.CLOSED) {
            shipment.setActualPickupAt(percolatedRecords.getLoadingCompletionTime());
            shipment = shipmentService.saveShipment(shipment);
        }
        stateMachineServiceRegistry.getService(StateMachineEntityType.SHIPMENT)
                .handleEvent(TenantContext.getCurrentTenant(), ShipmentLifecycleEvent.PICKED_UP.name(),
                        shipment.getId());
    }


    private void updateTrip(Trip trip, PickupTaskMessageDTO.PercolatedRecordDTO params, TaskStatus taskStatus) {
        if (taskStatus == TaskStatus.CLOSED) {
            trip.setActualStartAt(params.getLoadingCompletionTime());
            trip = tripService.saveTrip(trip);
        }
        taskPercolationHelperUtil.updateTripStatus(trip);
    }
}