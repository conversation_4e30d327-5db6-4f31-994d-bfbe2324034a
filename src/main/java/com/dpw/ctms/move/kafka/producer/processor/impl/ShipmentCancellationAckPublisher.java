package com.dpw.ctms.move.kafka.producer.processor.impl;

import com.dpw.ctms.move.kafka.producer.KafkaProducer;
import com.dpw.ctms.move.kafka.producer.processor.IEventRequestPublisher;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.request.message.ShipmentCancellationAckMessage;
import com.dpw.ctms.move.service.IEventProcessorService;
import com.dpw.tmsutils.annotation.LogExecutionTime;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.dpw.ctms.move.enums.MessageActionType.SHIPMENT_CANCELLATION_CONFIRMATION_EVENT;

@Component
@RequiredArgsConstructor
@Slf4j
@LogExecutionTime
public class ShipmentCancellationAckPublisher implements IEventRequestPublisher<ShipmentCancellationAckMessage> {
    private final IEventProcessorService<ShipmentCancellationAckMessage> eventProcessorService;
    private final KafkaProducer kafkaProducer;

    @PostConstruct
    public void init() {
        eventProcessorService.addRequestProcessor(SHIPMENT_CANCELLATION_CONFIRMATION_EVENT.name(),
                this);
    }

    @Override
    public boolean process(IntegratorMessageRequest<ShipmentCancellationAckMessage> request) {
        log.info("Inside ShipmentCancellationAckPublisher processing message");
        boolean isEventPublished = kafkaProducer.publishEvent(request);
        if (isEventPublished) {
            log.info("Message sent on topic: {}", request.getTransactionContext().getTopic());
        }
        return isEventPublished;
    }
}
