package com.dpw.ctms.move.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request object for checking document errors")
public class DocumentErrorRequest {
    
    @NotEmpty(message = "File identifiers list cannot be empty")
    @Schema(description = "List of file identifiers to check for errors", required = true, example = "[\"file-id-1\", \"file-id-2\"]")
    private List<String> fileIdentifiers;
}