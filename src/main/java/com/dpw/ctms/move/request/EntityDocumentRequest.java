package com.dpw.ctms.move.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request object for entity document lookup")
public class EntityDocumentRequest {
    
    @NotBlank(message = "Entity type is required")
    @Schema(description = "Type of the entity", example = "TASK", required = true)
    private String entityType;
    
    @NotBlank(message = "Entity code is required")
    @Schema(description = "Code of the entity", example = "TASK-1234", required = true)
    private String entityCode;
}