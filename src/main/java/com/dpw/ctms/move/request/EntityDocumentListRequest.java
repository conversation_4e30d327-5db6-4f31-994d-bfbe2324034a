package com.dpw.ctms.move.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request object containing list of entity document requests")
public class EntityDocumentListRequest {
    
    @Valid
    @Schema(description = "List of entity document requests", required = true)
    private List<EntityDocumentRequest> entityRequests;
}