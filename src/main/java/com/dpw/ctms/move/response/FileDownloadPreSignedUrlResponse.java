package com.dpw.ctms.move.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response containing presigned URLs for file identifiers")
public class FileDownloadPreSignedUrlResponse {
    
    @JsonProperty("data")
    @Schema(description = "Map of external document identifier to presigned URL", example = "{\"file-id-1\": \"https://example.com/presigned-url-1\"}")
    private Map<String, String> data;
}