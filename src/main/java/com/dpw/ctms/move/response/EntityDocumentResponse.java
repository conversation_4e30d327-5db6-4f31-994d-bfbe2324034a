package com.dpw.ctms.move.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response containing document details for entities")
public class EntityDocumentResponse {
    
    @JsonProperty("data")
    @Schema(description = "Response data")
    private ResponseData data;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Response data containing file details")
    public static class ResponseData {
        
        @JsonProperty("fileDetails")
        @Schema(description = "List of file details")
        private List<FileDetail> fileDetails;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "File detail information")
    public static class FileDetail {
        
        @JsonProperty("entityType")
        @Schema(description = "Type of the entity", example = "TASK")
        private String entityType;
        
        @JsonProperty("entityCode")
        @Schema(description = "Code of the entity", example = "TASK-1234")
        private String entityCode;
        
        @JsonProperty("asyncMappingUUID")
        @Schema(description = "Async mapping UUID", example = "43339835-f03b-43f9-8544-4e0f795ad991")
        private String asyncMappingUUID;
        
        @JsonProperty("fileName")
        @Schema(description = "Name of the file", example = "BOL-427dee387da940cb-knanrw.pdf")
        private String fileName;
        
        @JsonProperty("fileSize")
        @Schema(description = "Size of the file in bytes", example = "47524")
        private Integer fileSize;
        
        @JsonProperty("fileType")
        @Schema(description = "MIME type of the file", example = "application/pdf")
        private String fileType;
        
        @JsonProperty("externalDocumentIdentifier")
        @Schema(description = "External document identifier", example = "7e4c2694-22ff-4afc-90d0-89fdd0f1a9b8")
        private String externalDocumentIdentifier;
    }
}