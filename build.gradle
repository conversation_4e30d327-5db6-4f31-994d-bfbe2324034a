plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.5'
    id 'io.spring.dependency-management' version '1.1.4'
    id 'jacoco'
}

ext {
    querydslVersion = '5.0.0'
}

group = 'com.dpw'
version = '0.0.1-SNAPSHOT'

java {
    sourceCompatibility = '21'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}
dependencyManagement {
    dependencies {
        // transient dependency from 'org.springframework.boot:spring-boot-starter-web'
        dependency("org.apache.tomcat.embed:tomcat-embed-core:10.1.34")

        // transient dependency from 'tms-utils'
        dependency("org.springframework.security:spring-security-web:6.2.7")
        dependency("commons-io:commons-io:2.18.0")
        dependency("org.asynchttpclient:async-http-client:2.12.4")
        dependency("com.google.guava:guava:33.4.0-jre")
        dependency("com.nimbusds:nimbus-jose-jwt:10.0.2")
        dependency("org.bouncycastle:bcprov-jdk18on:1.80")
        dependency("io.netty:netty-handler:4.1.118.Final")
        dependency("org.springframework:spring-webflux:6.1.17")
        dependency("net.minidev:json-smart:2.5.2")
        dependency("io.netty:netty-common:4.1.118.Final")
        dependency("org.apache.commons:commons-compress:1.27.1")

        // transient dependency from 'tms-cargoes_tms_pojo'
        dependency("jakarta.el:jakarta.el-api:6.0.1")
    }
}

def vstsMavenAccessToken = project.findProperty('vstsMavenAccessToken')
repositories {
    mavenCentral()
    maven {
        url = uri('https://pkgs.dev.azure.com/dpwhotfsonline/_packaging/tms-artifacts/maven/v1')
        name = 'tms-artifacts'
        credentials {
            username = 'dpwhotfsonline'
            password = System.getenv("SYSTEM_ACCESSTOKEN") ?: '5amvLOKFtg4GZ2skRLnw4ESzqLO0XnziQOuGy5eon3D48kj8MjbLJQQJ99BEACAAAAATcomlAAASAZDO3sd4'
        }
    }
    mavenLocal()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'com.networknt:json-schema-validator:1.4.0'
    implementation 'org.postgresql:postgresql:42.7.3'
    implementation("org.flywaydb:flyway-core")
    implementation "org.springframework.boot:spring-boot-starter-webflux"
    implementation("org.springframework.statemachine:spring-statemachine-starter:4.0.0")
    implementation 'org.springframework.boot:spring-boot-starter-logging'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.14.1'
    implementation 'org.hibernate.orm:hibernate-envers'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'com.querydsl:querydsl-jpa:5.0.0:jakarta'
    annotationProcessor 'com.querydsl:querydsl-apt:5.0.0:jakarta'
    annotationProcessor 'jakarta.persistence:jakarta.persistence-api'
    annotationProcessor 'jakarta.annotation:jakarta.annotation-api'
    implementation("com.dpw.tms.pojo:tms-cargoes_tms_pojo:0.0.182-SNAPSHOT")
    implementation("com.dpw.tms.utils:tms-cargoes_tms_utils:0.0.37-SNAPSHOT")
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0"

    implementation ('com.github.javafaker:javafaker:1.0.2') {
        exclude group: "org.yaml", module: "snakeyaml"
    }
    // MapStruct dependencies
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'

    implementation("com.dpw:tms-utils:0.0.145")

    implementation("com.azure:azure-messaging-servicebus:7.17.10")
    implementation("io.hypersistence:hypersistence-utils-hibernate-60:3.9.0")
    implementation('javax.xml.bind:jaxb-api:2.3.1')
    implementation("com.newrelic.agent.java:newrelic-api:8.7.0")
    implementation("org.springframework.boot:spring-boot-starter-validation:3.4.4")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign:4.1.0")
    implementation 'org.apache.commons:commons-text:1.10.0'
    implementation 'com.netflix.hystrix:hystrix-core:1.5.18'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'com.bazaarvoice.jolt:jolt-core:0.1.8'
    implementation 'com.bazaarvoice.jolt:json-utils:0.1.8'



    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    //mapstruct
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'

    annotationProcessor "jakarta.persistence:jakarta.persistence-api"
    annotationProcessor "jakarta.annotation:jakarta.annotation-api"
    // Spring Boot Test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.boot:spring-boot-testcontainers'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation("org.springframework.statemachine:spring-statemachine-test:4.0.0")


    // Testcontainers
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:postgresql:1.19.8'
    testImplementation ('com.github.javafaker:javafaker:1.0.2') {
        exclude group: "org.yaml", module: "snakeyaml"
    }

    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    configurations.configureEach {
        resolutionStrategy {
            eachDependency { DependencyResolveDetails details ->
                if (details.requested.group == 'org.bouncycastle' && details.requested.name == 'bcprov-jdk15on') {
                    details.useTarget group: 'org.bouncycastle', name: 'bcprov-jdk18on', version: '1.80'
                }
                if (details.requested.group == 'org.bouncycastle' && details.requested.name == 'bcpkix-jdk15on') {
                    details.useTarget group: 'org.bouncycastle', name: 'bcpkix-jdk18on', version: '1.80'
                }
                if (details.requested.group == 'org.glassfish' && details.requested.name == 'jakarta.el') {
                    details.useTarget group: 'jakarta.el', name: 'jakarta.el-api', version: '6.0.1'
                }
            }
        }
    }
}


tasks.named('test') {
    useJUnitPlatform()
    finalizedBy("jacocoTestReport")
}

tasks.jacocoTestReport {
    dependsOn(tasks.test)
    reports {
        xml.required.set(true)
        html.required
    }
    sourceDirectories.setFrom(files(["src/main/java"]))
    classDirectories.setFrom(files(layout.buildDirectory.dir("classes/java/main")))
    executionData.setFrom(files(layout.buildDirectory.dir("jacoco/test.exec")))
}





